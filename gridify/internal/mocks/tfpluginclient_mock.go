// Code generated by MockGen. DO NOT EDIT.
// Source: ./internal/tfplugin/tfplugin.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	graphql "github.com/threefoldtech/tfgrid-sdk-go/grid-client/graphql"
	workloads "github.com/threefoldtech/tfgrid-sdk-go/grid-client/workloads"
	types "github.com/threefoldtech/tfgrid-sdk-go/grid-proxy/pkg/types"
)

// MockTFPluginClientInterface is a mock of TFPluginClientInterface interface.
type MockTFPluginClientInterface struct {
	ctrl     *gomock.Controller
	recorder *MockTFPluginClientInterfaceMockRecorder
}

// MockTFPluginClientInterfaceMockRecorder is the mock recorder for MockTFPluginClientInterface.
type MockTFPluginClientInterfaceMockRecorder struct {
	mock *MockTFPluginClientInterface
}

// NewMockTFPluginClientInterface creates a new mock instance.
func NewMockTFPluginClientInterface(ctrl *gomock.Controller) *MockTFPluginClientInterface {
	mock := &MockTFPluginClientInterface{ctrl: ctrl}
	mock.recorder = &MockTFPluginClientInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTFPluginClientInterface) EXPECT() *MockTFPluginClientInterfaceMockRecorder {
	return m.recorder
}

// CancelByProjectName mocks base method.
func (m *MockTFPluginClientInterface) CancelByProjectName(projectName string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CancelByProjectName", projectName)
	ret0, _ := ret[0].(error)
	return ret0
}

// CancelByProjectName indicates an expected call of CancelByProjectName.
func (mr *MockTFPluginClientInterfaceMockRecorder) CancelByProjectName(projectName interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CancelByProjectName", reflect.TypeOf((*MockTFPluginClientInterface)(nil).CancelByProjectName), projectName)
}

// DeployDeployment mocks base method.
func (m *MockTFPluginClientInterface) DeployDeployment(ctx context.Context, dl *workloads.Deployment) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeployDeployment", ctx, dl)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeployDeployment indicates an expected call of DeployDeployment.
func (mr *MockTFPluginClientInterfaceMockRecorder) DeployDeployment(ctx, dl interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeployDeployment", reflect.TypeOf((*MockTFPluginClientInterface)(nil).DeployDeployment), ctx, dl)
}

// DeployGatewayName mocks base method.
func (m *MockTFPluginClientInterface) DeployGatewayName(ctx context.Context, gw *workloads.GatewayNameProxy) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeployGatewayName", ctx, gw)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeployGatewayName indicates an expected call of DeployGatewayName.
func (mr *MockTFPluginClientInterfaceMockRecorder) DeployGatewayName(ctx, gw interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeployGatewayName", reflect.TypeOf((*MockTFPluginClientInterface)(nil).DeployGatewayName), ctx, gw)
}

// DeployNetwork mocks base method.
func (m *MockTFPluginClientInterface) DeployNetwork(ctx context.Context, znet *workloads.ZNet) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeployNetwork", ctx, znet)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeployNetwork indicates an expected call of DeployNetwork.
func (mr *MockTFPluginClientInterfaceMockRecorder) DeployNetwork(ctx, znet interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeployNetwork", reflect.TypeOf((*MockTFPluginClientInterface)(nil).DeployNetwork), ctx, znet)
}

// GetAvailableNode mocks base method.
func (m *MockTFPluginClientInterface) GetAvailableNode(ctx context.Context, options types.NodeFilter, rootfs uint64) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAvailableNode", ctx, options, rootfs)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAvailableNode indicates an expected call of GetAvailableNode.
func (mr *MockTFPluginClientInterfaceMockRecorder) GetAvailableNode(ctx, options, rootfs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAvailableNode", reflect.TypeOf((*MockTFPluginClientInterface)(nil).GetAvailableNode), ctx, options, rootfs)
}

// GetGridNetwork mocks base method.
func (m *MockTFPluginClientInterface) GetGridNetwork() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGridNetwork")
	ret0, _ := ret[0].(string)
	return ret0
}

// GetGridNetwork indicates an expected call of GetGridNetwork.
func (mr *MockTFPluginClientInterfaceMockRecorder) GetGridNetwork() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGridNetwork", reflect.TypeOf((*MockTFPluginClientInterface)(nil).GetGridNetwork))
}

// ListContractsOfProjectName mocks base method.
func (m *MockTFPluginClientInterface) ListContractsOfProjectName(projectName string) (graphql.Contracts, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListContractsOfProjectName", projectName)
	ret0, _ := ret[0].(graphql.Contracts)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListContractsOfProjectName indicates an expected call of ListContractsOfProjectName.
func (mr *MockTFPluginClientInterfaceMockRecorder) ListContractsOfProjectName(projectName interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListContractsOfProjectName", reflect.TypeOf((*MockTFPluginClientInterface)(nil).ListContractsOfProjectName), projectName)
}

// LoadGatewayNameFromGrid mocks base method.
func (m *MockTFPluginClientInterface) LoadGatewayNameFromGrid(ctx context.Context, nodeID uint32, name, deploymentName string) (workloads.GatewayNameProxy, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LoadGatewayNameFromGrid", ctx, nodeID, name, deploymentName)
	ret0, _ := ret[0].(workloads.GatewayNameProxy)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LoadGatewayNameFromGrid indicates an expected call of LoadGatewayNameFromGrid.
func (mr *MockTFPluginClientInterfaceMockRecorder) LoadGatewayNameFromGrid(ctx context.Context, nodeID, name, deploymentName interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LoadGatewayNameFromGrid", reflect.TypeOf((*MockTFPluginClientInterface)(nil).LoadGatewayNameFromGrid), ctx, nodeID, name, deploymentName)
}

// LoadVMFromGrid mocks base method.
func (m *MockTFPluginClientInterface) LoadVMFromGrid(ctx context.Context, nodeID uint32, name, deploymentName string) (workloads.VM, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LoadVMFromGrid", ctx, nodeID, name, deploymentName)
	ret0, _ := ret[0].(workloads.VM)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LoadVMFromGrid indicates an expected call of LoadVMFromGrid.
func (mr *MockTFPluginClientInterfaceMockRecorder) LoadVMFromGrid(ctx context.Context, nodeID, name, deploymentName interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LoadVMFromGrid", reflect.TypeOf((*MockTFPluginClientInterface)(nil).LoadVMFromGrid), ctx, nodeID, name, deploymentName)
}

// SetState mocks base method.
func (m *MockTFPluginClientInterface) SetState(nodeID uint32, contractIDs []uint64) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetState", nodeID, contractIDs)
}

// SetState indicates an expected call of SetState.
func (mr *MockTFPluginClientInterfaceMockRecorder) SetState(nodeID, contractIDs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetState", reflect.TypeOf((*MockTFPluginClientInterface)(nil).SetState), nodeID, contractIDs)
}
