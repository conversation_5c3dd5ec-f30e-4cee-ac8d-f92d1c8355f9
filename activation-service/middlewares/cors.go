// Package middlewares for middleware between api and backend
package middlewares

import "net/http"

// EnableCors enables cors middleware
func EnableCors(h http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		setupCorsResponse(w, r)
		h.ServeHTTP(w, r)
	})
}

func setupCorsResponse(w http.ResponseWriter, req *http.Request) {
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "POST, GET, OPTIONS, PUT, DELETE")
	w.<PERSON><PERSON>().Set("Access-Control-Allow-Headers", "Accept, Content-Type, Content-Length, Authorization")

	if req.Method == "OPTIONS" {
		w.WriteHeader(http.StatusOK)
		return
	}
}
