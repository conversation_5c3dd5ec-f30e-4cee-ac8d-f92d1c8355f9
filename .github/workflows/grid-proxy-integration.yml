name: Proxy integration tests

on:
  push:
  pull_request:
    types: [opened, reopened, review_requested]
  schedule:
    - cron: 25 5 * * *
  workflow_dispatch:

jobs:
  test:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: grid-proxy
    services:
      postgres:
        image: postgres
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: tfgrid-graphql
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
    steps:
      - uses: actions/checkout@v5

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: 1.23

      - name: Build
        run: |
          export PATH=/home/<USER>/go/bin:$PATH
          export GIT_COMMIT=$(git rev-list -1 HEAD) 
          go build -ldflags "-X main.GitCommit=$GIT_COMMIT" cmds/proxy_server/main.go
        env:
          GO111MODULE: on

      - name: Test
        env:
          MNEMONICS: ${{ secrets.MNEMONICS }}
        run: |
          export PATH=/home/<USER>/go/bin:$PATH
          pushd tools/db
          go run . --seed 13 --postgres-host localhost --postgres-db tfgrid-graphql --postgres-password postgres --postgres-user postgres --reset
          popd
          go run cmds/proxy_server/main.go -no-cert -no-indexer --address :8080 --log-level debug --postgres-host localhost --postgres-db tfgrid-graphql --postgres-password postgres --postgres-user postgres --mnemonics "$MNEMONICS" &
          sleep 10
          pushd tests/queries
          go test -v --seed 13 -no-modify --postgres-host localhost --postgres-db tfgrid-graphql --postgres-password postgres --postgres-user postgres --endpoint http://localhost:8080
          popd
