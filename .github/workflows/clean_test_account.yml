name: Testing account cleanup

defaults:
  run:
    working-directory: grid-cli
on:
  schedule:
    - cron: "0 9 * * *"
  workflow_dispatch:

jobs:
  test:
    runs-on: ubuntu-latest
    timeout-minutes: 20
    strategy:
      fail-fast: false
      matrix:
        network: ["dev", "qa", "test", "main"]

    steps:
      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: "1.23"
        id: go

      - name: Check out code into the Go module directory
        uses: actions/checkout@v5

      - name: CLI build
        run: make build

      - name: CLI login
        run: /bin/bash -c  "{ echo ${{ secrets.MNEMONICS }}; echo ${{ matrix.network }}; } | bin/tfcmd login"

      - name: Clean contracts
        run: bin/tfcmd cancel contracts --all
