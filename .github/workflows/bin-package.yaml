# Builds a single runtime package, this
# workflow is only invoked from the `bins.yaml` file
# to build the configured packages
#
# the built binary is ALWAYS published to tf-autobuilder
# and then tagged with a certain tag. this can be the version
# of the release or the `short sha` of the head, if on main
# branch
name: Build Extra Binary

on:
  workflow_call:
    inputs:
      package:
        description: "package to build"
        required: true
        type: string
    secrets:
      token:
        required: true
jobs:
  builder:
    name: builder
    runs-on: ubuntu-latest
    container: ubuntu:20.04
    steps:
      - name: Checkout code into the Go module directory
        uses: actions/checkout@v1
      # the tag step only extract the correct version
      # tag to use. this is the short sha in case of
      # a branch, or the tag name in case of "release"
      # the value is then stored as `reference`
      # and then accessed later in the workflow
      - name: Set tag of build
        id: tag
        shell: bash
        run: |
          ref="${{ github.ref }}"
          if [ "${{ github.ref_type }}" = "tag" ]; then
            echo "reference=${ref#refs/tags/}" >> $GITHUB_OUTPUT
          else
            reference="${{ github.sha }}"
            echo "reference=${reference:0:7}" >> $GITHUB_OUTPUT
          fi
      - name: Setup basesystem
        shell: bash
        run: |
          apt update
          cd bins
          ./bins-extra.sh --package basesystem
      - name: Build package (${{ inputs.package }})
        id: package
        shell: bash
        run: |
          cd bins
          ./bins-extra.sh --package ${{ inputs.package }}
      - name: Publish flist (tf-autobuilder, ${{ steps.package.outputs.name }})
        if: success() && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/testdeploy' || startsWith(github.ref, 'refs/tags/v'))
        uses: threefoldtech/publish-flist@master
        with:
          token: ${{ secrets.token }}
          action: publish
          user: tf-autobuilder
          root: bins/releases/${{ inputs.package }}
          name: ${{ steps.package.outputs.name }}.flist
      - name: Tagging
        uses: threefoldtech/publish-flist@master
        if: success() && (github.ref == 'refs/heads/main'  || github.ref == 'refs/heads/testdeploy'|| startsWith(github.ref, 'refs/tags/v'))
        with:
          token: ${{ secrets.token }}
          action: tag
          user: tf-autobuilder
          name: ${{ steps.tag.outputs.reference }}/${{ inputs.package }}.flist
          target: tf-autobuilder/${{ steps.package.outputs.name }}.flist
