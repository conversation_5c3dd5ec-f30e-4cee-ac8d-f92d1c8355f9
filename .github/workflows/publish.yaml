# the publish workflow builds zos binaries
# and tag them correctly
# the build is triggered when pushing to any
# branch, but the tagging of the build only happens
# either on main or when a tag is created
name: Release
on:
  push:
    branches:
      - "*"
    tags:
      - "v*"
jobs:
  build:
    name: Build and upload
    runs-on: ubuntu-latest
    steps:
      - name: Set up Go 1.21
        uses: actions/setup-go@v1
        with:
          go-version: 1.21
        id: go

      - name: Checkout code into the Go module directory
        uses: actions/checkout@v1

      - name: Build binaries
        run: |
          cd cmds
          make
        env:
          GO111MODULE: on
      # the tag step only extract the correct version
      # tag to use. this is the short sha in case of
      # a branch, or the tag name in case of "release"
      # the value is then stored as `reference`
      # and then accessed later in the workflow
      - name: Set tag of build
        id: tag
        run: |
          ref="${{ github.ref }}"
          if [ "${{ github.ref_type }}" = "tag" ]; then
            echo "reference=${ref#refs/tags/}" >> $GITHUB_OUTPUT
          else
            reference="${{ github.sha }}"
            echo "reference=${reference:0:7}" >> $GITHUB_OUTPUT
          fi

      - name: Set version of build
        id: version
        run: |
          echo "version=v$(date +%y%m%d.%-H%M%S.0)" >> $GITHUB_OUTPUT

      - name: Collecting files
        run: |
          scripts/collect.sh ${{ github.workspace }}/archive

      - name: Publish flist (zos:${{ steps.version.outputs.version }}.flist)
        if: success()
        uses: threefoldtech/publish-flist@master
        with:
          token: ${{ secrets.HUB_JWT }}
          action: publish
          user: tf-autobuilder
          root: archive
          name: zos:${{ steps.version.outputs.version }}.flist

      # we tag only if and only if we merged to main
      # in that case the tag will be the short sha.
      # or if we tagged a certain version and that
      # will use the tag value (has to start with v)
      - name: Tagging
        uses: threefoldtech/publish-flist@master
        if: success() && (github.ref == 'refs/heads/main' || startsWith(github.ref, 'refs/tags/v')|| github.ref == 'refs/heads/testdeploy')
        with:
          token: ${{ secrets.HUB_JWT }}
          action: tag
          user: tf-autobuilder
          name: ${{ steps.tag.outputs.reference }}/zos.flist
          target: tf-autobuilder/zos:${{ steps.version.outputs.version }}.flist

      # only for main branch (devnet)
      # this basically releases this build to devnet
      - name: Cross tagging (development)
        if: success() &&  (github.ref == 'refs/heads/main'|| github.ref == 'refs/heads/testdeploy')
        uses: threefoldtech/publish-flist@master
        with:
          token: ${{ secrets.HUB_JWT }}
          action: crosstag
          user: tf-zos
          name: development
          target: tf-autobuilder/${{ steps.tag.outputs.reference }}
