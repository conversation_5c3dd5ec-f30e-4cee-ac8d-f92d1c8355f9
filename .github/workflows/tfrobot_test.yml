name: Tfrobot

defaults:
  run:
    working-directory: tfrobot
on:
  schedule:
    - cron: 0 4 * * *
  workflow_dispatch:

jobs:
  test:
    runs-on: ubuntu-latest
    timeout-minutes: 60
    strategy:
      fail-fast: false

    steps:
      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: "1.23"
        id: go

      - name: Check out code into the Go module directory
        uses: actions/checkout@v5

      - name: Get dependencies
        run: |
          go mod download

      - name: Test
        env:
          MNEMONIC: ${{ secrets.MNEMONICS }}
          NETWORK: main
        run: |
            go run main.go deploy -c ./example/test.yaml

      - name: Cleanup
        if: always()
        env:
          MNEMONIC: ${{ secrets.MNEMONICS }}
          NETWORK: main
        run: |
            sleep 120 # sleep to make sure graphql is up to date
            go run main.go cancel -c ./example/test.yaml -d
