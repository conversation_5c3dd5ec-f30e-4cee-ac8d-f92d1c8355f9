name: Test sdk

on:
  push:
  workflow_dispatch:

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: "1.23"

      - name: Check out code into the Go module directory
        uses: actions/checkout@v5

      - name: Get dependencies
        run: cd ${{ matrix.dir }} && go mod download

      - name: Test ${{ matrix.dir }}
        env:
          MNEMONICS: ${{ secrets.MNEMONICS }}
          NETWORK: dev
        run: cd ${{ matrix.dir }} && go test -v `go list ./... | grep -v integration_tests`

      - name: Test grid-proxy
        run: cd grid-proxy && go test -v ./pkg/client

    strategy:
      fail-fast: false
      matrix:
        dir:
          - activation-service
          - farmerbot 
          - grid-cli
          - grid-client
          - gridify
          - monitoring-bot
          - rmb-sdk-go
          - user-contracts-mon 
          - tfrobot
