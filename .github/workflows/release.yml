name: Release

on:
  push:
    tags:
      - v*

jobs:
  goreleaser:
    runs-on: ubuntu-latest
    permissions:
      contents: write
    steps:
      - name: Checkout
        uses: actions/checkout@v5
        with:
          submodules: "true"

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: 1.23

      - name: Run GoReleaser
        uses: goreleaser/goreleaser-action@v6.3.0
        with:
          version: latest
          args: release --clean
        env:
          # GitHub sets this automatically
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
