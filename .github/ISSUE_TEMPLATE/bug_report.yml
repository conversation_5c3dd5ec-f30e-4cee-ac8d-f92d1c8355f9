name: Bug Report
description: Create a bug report
title: "🐞 [Bug]: "
labels: ["type_bug"]
body:
  - type: markdown
    attributes:
      value: |
        Please fill the fields to help us solving your bug! Don't forget to add the right label

  - type: textarea
    id: what-happened
    attributes:
      label: What happened?
      description: Also tell us, what did you expect to happen?
      placeholder: Describe your bug!
    validations:
      required: true

  - type: dropdown
    id: networks
    attributes:
      label: which network/s did you face the problem on?
      multiple: true
      options:
        - Dev
        - QA
        - Test
        - Main
    validations:
      required: true

  - type: input
    id: twin_id
    attributes:
      label: Twin ID/s
    validations:
      required: false
      
  - type: input
    id: version
    attributes:
      label: Version
    validations:
      required: false
      
  - type: input
    id: node_id
    attributes:
      label: Node ID/s
    validations:
      required: false

  - type: input
    id: farm_id
    attributes:
      label: Farm ID/s
    validations:
      required: false

  - type: input
    id: contract_id
    attributes:
      label: Contract ID/s
    validations:
      required: false

  - type: textarea
    id: logs
    attributes:
      label: Relevant log output
      description: Please copy and paste any relevant log output. This will be automatically formatted into code, so no need for backticks.
      render: console
    validations:
      required: true
