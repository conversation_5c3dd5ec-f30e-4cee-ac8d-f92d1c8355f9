# See GitHub's docs for more information on this file:
# https://docs.github.com/en/free-pro-team@latest/github/administering-a-repository/configuration-options-for-dependency-updates
version: 2
updates:
  # Maintain dependencies for GitHub Actions
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      # Check for updates to GitHub Actions every weekday
      interval: "daily"

  - package-ecosystem: "gomod"
    directory: "/activation-service"
    schedule:
      interval: "daily"

  - package-ecosystem: "gomod"
    directory: "/grid-cli"
    schedule:
      interval: "daily"

  - package-ecosystem: "gomod"
    directory: "/grid-client"
    schedule:
      interval: "daily"

  - package-ecosystem: "gomod"
    directory: "/grid-proxy"
    schedule:
      interval: "daily"

  - package-ecosystem: "gomod"
    directory: "/gridify"
    schedule:
      interval: "daily"

  - package-ecosystem: "gomod"
    directory: "/monitoring-bot"
    schedule:
      interval: "daily"

  - package-ecosystem: "gomod"
    directory: "/rmb-sdk-go"
    schedule:
      interval: "daily"
