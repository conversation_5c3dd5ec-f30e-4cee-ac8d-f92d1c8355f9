// Code generated by MockGen. DO NOT EDIT.
// Source: internal/substrate.go

// Package mocks is a generated GoMock package.
package mocks

import (
	reflect "reflect"

	types "github.com/centrifuge/go-substrate-rpc-client/v4/types"
	gomock "github.com/golang/mock/gomock"
	substrate "github.com/threefoldtech/tfchain/clients/tfchain-client-go"
)

// MockSubstrate is a mock of Substrate interface.
type MockSubstrate struct {
	ctrl     *gomock.Controller
	recorder *MockSubstrateMockRecorder
}

// MockSubstrateMockRecorder is the mock recorder for MockSubstrate.
type MockSubstrateMockRecorder struct {
	mock *MockSubstrate
}

// NewMockSubstrate creates a new mock instance.
func NewMockSubstrate(ctrl *gomock.Controller) *MockSubstrate {
	mock := &MockSubstrate{ctrl: ctrl}
	mock.recorder = &MockSubstrateMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSubstrate) EXPECT() *MockSubstrateMockRecorder {
	return m.recorder
}

// GetDedicatedNodePrice mocks base method.
func (m *MockSubstrate) GetDedicatedNodePrice(nodeID uint32) (uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDedicatedNodePrice", nodeID)
	ret0, _ := ret[0].(uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDedicatedNodePrice indicates an expected call of GetDedicatedNodePrice.
func (mr *MockSubstrateMockRecorder) GetDedicatedNodePrice(nodeID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDedicatedNodePrice", reflect.TypeOf((*MockSubstrate)(nil).GetDedicatedNodePrice), nodeID)
}

// GetFarm mocks base method.
func (m *MockSubstrate) GetFarm(id uint32) (*substrate.Farm, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFarm", id)
	ret0, _ := ret[0].(*substrate.Farm)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFarm indicates an expected call of GetFarm.
func (mr *MockSubstrateMockRecorder) GetFarm(id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFarm", reflect.TypeOf((*MockSubstrate)(nil).GetFarm), id)
}

// GetNode mocks base method.
func (m *MockSubstrate) GetNode(nodeID uint32) (*substrate.Node, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNode", nodeID)
	ret0, _ := ret[0].(*substrate.Node)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNode indicates an expected call of GetNode.
func (mr *MockSubstrateMockRecorder) GetNode(nodeID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNode", reflect.TypeOf((*MockSubstrate)(nil).GetNode), nodeID)
}

// GetNodeRentContract mocks base method.
func (m *MockSubstrate) GetNodeRentContract(nodeID uint32) (uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNodeRentContract", nodeID)
	ret0, _ := ret[0].(uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNodeRentContract indicates an expected call of GetNodeRentContract.
func (mr *MockSubstrateMockRecorder) GetNodeRentContract(nodeID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNodeRentContract", reflect.TypeOf((*MockSubstrate)(nil).GetNodeRentContract), nodeID)
}


// GetNodeContracts mocks base method.
func (m *MockSubstrate) GetNodeContracts(nodeID uint32) ([]types.U64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNodeContracts", nodeID)
	ret0, _ := ret[0].([]types.U64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNodeContracts indicates an expected call of GetNodeContracts.
func (mr *MockSubstrateMockRecorder) GetNodeContracts(nodeID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNodeContracts", reflect.TypeOf((*MockSubstrate)(nil).GetNodeContracts), nodeID)
}

// GetNodes mocks base method.
func (m *MockSubstrate) GetNodes(farmID uint32) ([]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNodes", farmID)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNodes indicates an expected call of GetNodes.
func (mr *MockSubstrateMockRecorder) GetNodes(farmID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNodes", reflect.TypeOf((*MockSubstrate)(nil).GetNodes), farmID)
}

// GetPowerTarget mocks base method.
func (m *MockSubstrate) GetPowerTarget(nodeID uint32) (substrate.NodePower, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPowerTarget", nodeID)
	ret0, _ := ret[0].(substrate.NodePower)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPowerTarget indicates an expected call of GetPowerTarget.
func (mr *MockSubstrateMockRecorder) GetPowerTarget(nodeID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPowerTarget", reflect.TypeOf((*MockSubstrate)(nil).GetPowerTarget), nodeID)
}

// GetTwinByPubKey mocks base method.
func (m *MockSubstrate) GetTwinByPubKey(publicKey []byte) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTwinByPubKey", publicKey)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTwinByPubKey indicates an expected call of GetTwinByPubKey.
func (mr *MockSubstrateMockRecorder) GetTwinByPubKey(publicKey interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTwinByPubKey", reflect.TypeOf((*MockSubstrate)(nil).GetTwinByPubKey), publicKey)
}

// SetNodePowerTarget mocks base method.
func (m *MockSubstrate) SetNodePowerTarget(identity substrate.Identity, nodeID uint32, up bool) (types.Hash, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetNodePowerTarget", identity, nodeID, up)
	ret0, _ := ret[0].(types.Hash)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetNodePowerTarget indicates an expected call of SetNodePowerTarget.
func (mr *MockSubstrateMockRecorder) SetNodePowerTarget(identity, nodeID, up interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetNodePowerTarget", reflect.TypeOf((*MockSubstrate)(nil).SetNodePowerTarget), identity, nodeID, up)
}
