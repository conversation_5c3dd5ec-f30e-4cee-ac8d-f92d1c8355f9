// Code generated by MockGen. DO NOT EDIT.
// Source: internal/proxy_client.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	types "github.com/threefoldtech/tfgrid-sdk-go/grid-proxy/pkg/types"
)

// MockProxyClient is a mock of ProxyClient interface.
type MockProxyClient struct {
	ctrl     *gomock.Controller
	recorder *MockProxyClientMockRecorder
}

// MockProxyClientMockRecorder is the mock recorder for MockProxyClient.
type MockProxyClientMockRecorder struct {
	mock *MockProxyClient
}

// NewMockProxyClient creates a new mock instance.
func NewMockProxyClient(ctrl *gomock.Controller) *MockProxyClient {
	mock := &MockProxyClient{ctrl: ctrl}
	mock.recorder = &MockProxyClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockProxyClient) EXPECT() *MockProxyClientMockRecorder {
	return m.recorder
}

// Node mocks base method.
func (m *MockProxyClient) Node(ctx context.Context, nodeID uint32) (types.NodeWithNestedCapacity, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Node", ctx, nodeID)
	ret0, _ := ret[0].(types.NodeWithNestedCapacity)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Node indicates an expected call of Node.
func (mr *MockProxyClientMockRecorder) Node(ctx, nodeID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Node", reflect.TypeOf((*MockProxyClient)(nil).Node), ctx, nodeID)
}
