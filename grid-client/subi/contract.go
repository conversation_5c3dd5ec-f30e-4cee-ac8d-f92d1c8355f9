// Package subi for substrate client
package subi

import substrate "github.com/threefoldtech/tfchain/clients/tfchain-client-go"

// Contract is for contract implementation
type Contract struct {
	*substrate.Contract
}

// IsDeleted checks if contract is deleted
func (c *Contract) IsDeleted() bool {
	return c.State.IsDeleted
}

// IsCreated checks if contract is created
func (c *Contract) IsCreated() bool {
	return c.State.IsCreated
}

// TwinID returns contract's twin ID
func (c *Contract) TwinID() uint32 {
	return uint32(c.Contract.TwinID)
}

// PublicIPCount returns contract's public IPs count
func (c *Contract) PublicIPCount() uint32 {
	return uint32(c.ContractType.NodeContract.PublicIPsCount)
}
