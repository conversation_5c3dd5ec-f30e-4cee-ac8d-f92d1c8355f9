// Package subi for substrate client
package subi

import (
	"context"
	"sync"

	"github.com/centrifuge/go-substrate-rpc-client/v4/types"
	"github.com/pkg/errors"
	substrate "github.com/threefoldtech/tfchain/clients/tfchain-client-go"
)

// ManagerInterface for substrate manager
type ManagerInterface interface {
	substrate.Manager
	SubstrateExt() (SubstrateImpl, error)
}

// Manager is substrate manager struct
type Manager struct {
	substrate.Manager
	// versioned subdev.Manager
}

// NewManager returns a new substrate manager
func NewManager(url ...string) Manager {
	return Manager{substrate.NewManager(url...)}
}

// SubstrateExt returns a substrate implementation to be used
func (m *Manager) SubstrateExt() (*SubstrateImpl, error) {
	sub, err := m.Substrate()
	return &SubstrateImpl{Substrate: sub}, err
}

// SubstrateExt interface for substrate client
type SubstrateExt interface {
	CancelContract(identity substrate.Identity, contractID uint64) error
	CreateNodeContract(identity substrate.Identity, node uint32, body string, hash string, publicIPs uint32, solutionProviderID *uint64) (uint64, error)
	UpdateNodeContract(identity substrate.Identity, contract uint64, body string, hash string) (uint64, error)
	Close()
	GetTwinByPubKey(pk []byte) (uint32, error)

	EnsureContractCanceled(identity substrate.Identity, contractID uint64) error
	DeleteInvalidContracts(contracts map[uint32]uint64) error
	IsValidContract(contractID uint64) (bool, error)
	InvalidateNameContract(
		ctx context.Context,
		identity substrate.Identity,
		contractID uint64,
		name string,
	) (uint64, error)
	GetContract(id uint64) (Contract, error)
	GetNodeTwin(id uint32) (uint32, error)
	GetNode(id uint32) (*substrate.Node, error)
	CreateNameContract(identity substrate.Identity, name string) (uint64, error)
	GetAccount(identity substrate.Identity) (substrate.AccountInfo, error)
	GetBalance(identity substrate.Identity) (balance substrate.Balance, err error)
	GetTFTPrice() (balance types.U32, err error)
	GetPricingPolicy(policyID uint32) (pricingPolicy substrate.PricingPolicy, err error)
	GetTwinPK(twinID uint32) ([]byte, error)
	GetContractIDByNameRegistration(name string) (uint64, error)
	BatchCreateContract(identity substrate.Identity, contractsData []substrate.BatchCreateContractData) ([]uint64, *int, error)
	BatchAllCreateContract(identity substrate.Identity, contractsData []substrate.BatchCreateContractData) ([]uint64, error)
	BatchCancelContract(identity substrate.Identity, contracts []uint64) error
	GetContractPaymentState(id uint64) (substrate.ContractPaymentState, error)
	GetContractBillingInfo(id uint64) (substrate.ContractBillingInfo, error)
	GetNodeContractResources(id uint64) (substrate.NodeContractResources, error)
	GetNodeRentContract(id uint32) (uint64, error)
	GetDedicatedNodePrice(nodeID uint32) (uint64, error)
	GetNodeContracts(nodeID uint32) ([]types.U64, error)
}

// SubstrateImpl struct to use dev substrate
type SubstrateImpl struct {
	*substrate.Substrate
	m sync.Mutex
}

// GetAccount returns the user's account
func (s *SubstrateImpl) GetAccount(identity substrate.Identity) (substrate.AccountInfo, error) {
	res, err := s.Substrate.GetAccount(identity)
	return res, normalizeNotFoundErrors(err)
}

// GetBalance returns the user's balance
func (s *SubstrateImpl) GetBalance(identity substrate.Identity) (balance substrate.Balance, err error) {
	accountAddress, err := substrate.FromAddress(identity.Address())
	if err != nil {
		return
	}

	balance, err = s.Substrate.GetBalance(accountAddress)
	return balance, normalizeNotFoundErrors(err)
}

// GetTFTPrice returns the TFT's price
func (s *SubstrateImpl) GetTFTPrice() (balance types.U32, err error) {
	price, err := s.Substrate.GetTFTPrice()
	return price, normalizeNotFoundErrors(err)
}

// GetPricingPolicy returns a pricing policy
func (s *SubstrateImpl) GetPricingPolicy(policyID uint32) (pricingPolicy substrate.PricingPolicy, err error) {
	pricingPolicy, err = s.Substrate.GetPricingPolicy(policyID)
	return pricingPolicy, normalizeNotFoundErrors(err)
}

// GetNodeTwin returns the twin ID for a node ID
func (s *SubstrateImpl) GetNodeTwin(nodeID uint32) (uint32, error) {
	node, err := s.GetNode(nodeID)
	if err != nil {
		return 0, normalizeNotFoundErrors(err)
	}
	return uint32(node.TwinID), nil
}

// GetTwinPK returns twin's public key
func (s *SubstrateImpl) GetTwinPK(id uint32) ([]byte, error) {
	twin, err := s.GetTwin(id)
	if err != nil {
		return nil, normalizeNotFoundErrors(err)
	}
	return twin.Account.PublicKey(), nil
}

// CreateNameContract creates a new name contract
func (s *SubstrateImpl) CreateNameContract(identity substrate.Identity, name string) (uint64, error) {
	s.m.Lock()
	defer s.m.Unlock()

	return s.Substrate.CreateNameContract(identity, name)
}

// GetContractIDByNameRegistration returns contract ID using its name
func (s *SubstrateImpl) GetContractIDByNameRegistration(name string) (uint64, error) {
	res, err := s.Substrate.GetContractIDByNameRegistration(name)
	return res, normalizeNotFoundErrors(err)
}

// CreateNodeContract creates a new node contract
func (s *SubstrateImpl) CreateNodeContract(identity substrate.Identity, node uint32, body string, hash string, publicIPs uint32, solutionProviderID *uint64) (uint64, error) {
	s.m.Lock()
	defer s.m.Unlock()

	res, err := s.Substrate.CreateNodeContract(identity, node, body, hash, publicIPs, solutionProviderID)
	return res, normalizeNotFoundErrors(err)
}

// UpdateNodeContract updates a new name contract
func (s *SubstrateImpl) UpdateNodeContract(identity substrate.Identity, contract uint64, body string, hash string) (uint64, error) {
	s.m.Lock()
	defer s.m.Unlock()

	res, err := s.Substrate.UpdateNodeContract(identity, contract, body, hash)
	return res, normalizeNotFoundErrors(err)
}

// GetContract returns a contract given its ID
func (s *SubstrateImpl) GetContract(contractID uint64) (Contract, error) {
	contract, err := s.Substrate.GetContract(contractID)
	return Contract{contract}, normalizeNotFoundErrors(err)
}

// CancelContract cancels a contract
func (s *SubstrateImpl) CancelContract(identity substrate.Identity, contractID uint64) error {
	if contractID == 0 {
		return nil
	}

	s.m.Lock()
	defer s.m.Unlock()

	if err := s.Substrate.CancelContract(identity, contractID); err != nil {
		return normalizeNotFoundErrors(err)
	}
	return nil
}

// EnsureContractCanceled ensures a canceled contract
func (s *SubstrateImpl) EnsureContractCanceled(identity substrate.Identity, contractID uint64) error {
	if contractID == 0 {
		return nil
	}

	s.m.Lock()
	defer s.m.Unlock()

	if err := s.Substrate.CancelContract(identity, contractID); err != nil {
		return normalizeNotFoundErrors(err)
	}
	return nil
}

// DeleteInvalidContracts deletes invalid contracts
func (s *SubstrateImpl) DeleteInvalidContracts(contracts map[uint32]uint64) error {
	for node, contractID := range contracts {
		valid, err := s.IsValidContract(contractID)
		// TODO: handle pause
		if err != nil {
			return normalizeNotFoundErrors(err)
		}
		if !valid {
			delete(contracts, node)
		}
	}
	return nil
}

// IsValidContract checks if a contract is invalid
func (s *SubstrateImpl) IsValidContract(contractID uint64) (bool, error) {
	if contractID == 0 {
		return false, nil
	}
	contract, err := s.Substrate.GetContract(contractID)
	err = normalizeNotFoundErrors(err)
	// TODO: handle pause
	if errors.Is(err, substrate.ErrNotFound) || (contract != nil && !contract.State.IsCreated) {
		return false, nil
	} else if err != nil {
		return true, errors.Wrapf(err, "could not get contract %d info", contractID)
	}
	return true, nil
}

// BatchCreateContract creates a batch of contracts non-atomically
func (s *SubstrateImpl) BatchCreateContract(identity substrate.Identity, contractsData []substrate.BatchCreateContractData) ([]uint64, *int, error) {
	return s.Substrate.BatchCreateContract(identity, contractsData)
}

// BatchAllCreateContract creates a batch of contracts atomically
func (s *SubstrateImpl) BatchAllCreateContract(identity substrate.Identity, contractsData []substrate.BatchCreateContractData) ([]uint64, error) {
	return s.Substrate.BatchAllCreateContract(identity, contractsData)
}

// BatchCancelContract cancels a batch of contracts
func (s *SubstrateImpl) BatchCancelContract(identity substrate.Identity, contracts []uint64) error {
	return s.Substrate.BatchCancelContract(identity, contracts)
}

// InvalidateNameContract invalidate a name contract
func (s *SubstrateImpl) InvalidateNameContract(
	ctx context.Context,
	identity substrate.Identity,
	contractID uint64,
	name string,
) (uint64, error) {
	if contractID == 0 {
		return 0, nil
	}
	contract, err := s.Substrate.GetContract(contractID)
	err = normalizeNotFoundErrors(err)
	if errors.Is(err, substrate.ErrNotFound) {
		return 0, nil
	}
	if err != nil {
		return 0, errors.Wrap(err, "could not get name contract info")
	}
	// TODO: paused?
	if !contract.State.IsCreated {
		return 0, nil
	}

	s.m.Lock()
	defer s.m.Unlock()

	if contract.ContractType.NameContract.Name != name {
		err := s.Substrate.CancelContract(identity, contractID)
		if err != nil {
			return 0, errors.Wrap(normalizeNotFoundErrors(err), "failed to cleanup unmatched name contract")
		}
		return 0, nil
	}

	return contractID, nil
}

// Close closes substrate
func (s *SubstrateImpl) Close() {
	s.Substrate.Close()
}

func normalizeNotFoundErrors(err error) error {
	if errors.Is(err, substrate.ErrNotFound) {
		return substrate.ErrNotFound
	}

	if errors.Is(err, substrate.ErrAccountNotFound) {
		return substrate.ErrAccountNotFound
	}
	return err
}

// Get node
func (s *SubstrateImpl) GetNode(id uint32) (*substrate.Node, error) {
	return s.Substrate.GetNode(id)
}

// Get contract payment state
func (s *SubstrateImpl) GetContractPaymentState(id uint64) (substrate.ContractPaymentState, error) {
	return s.Substrate.GetContractPaymentState(id)
}

// Get contract billing info
func (s *SubstrateImpl) GetContractBillingInfo(id uint64) (substrate.ContractBillingInfo, error) {
	return s.Substrate.GetContractBillingInfo(id)
}

// Get node contract resources by contract Id
func (s *SubstrateImpl) GetNodeContractResources(id uint64) (substrate.NodeContractResources, error) {
	return s.Substrate.GetNodeContractResources(id)
}

func (s *SubstrateImpl) GetNodeRentContract(id uint32) (uint64, error) {
	return s.Substrate.GetNodeRentContract(id)
}

func (s *SubstrateImpl) GetDedicatedNodePrice(nodeID uint32) (uint64, error) {
	return s.Substrate.GetDedicatedNodePrice(nodeID)
}

func (s *SubstrateImpl) GetNodeContracts(nodeID uint32) ([]types.U64, error) {
	return s.Substrate.GetNodeContracts(nodeID)
}
