// Code generated by MockGen. DO NOT EDIT.
// Source: ./deployer/deployer.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	zosTypes "github.com/threefoldtech/tfgrid-sdk-go/grid-client/zos"
)

// MockDeployer is a mock of MockDeployer interface.
type MockDeployer struct {
	ctrl     *gomock.Controller
	recorder *MockDeployerMockRecorder
}

// MockDeployerMockRecorder is the mock recorder for MockDeployer.
type MockDeployerMockRecorder struct {
	mock *MockDeployer
}

// NewMockDeployer creates a new mock instance.
func NewMockDeployer(ctrl *gomock.Controller) *MockDeployer {
	mock := &MockDeployer{ctrl: ctrl}
	mock.recorder = &MockDeployerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDeployer) EXPECT() *MockDeployerMockRecorder {
	return m.recorder
}

// BatchDeploy mocks base method.
func (m *MockDeployer) BatchDeploy(ctx context.Context, deployments map[uint32][]zosTypes.Deployment, deploymentsSolutionProvider map[uint32][]*uint64) (map[uint32][]zosTypes.Deployment, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchDeploy", ctx, deployments, deploymentsSolutionProvider)
	ret0, _ := ret[0].(map[uint32][]zosTypes.Deployment)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchDeploy indicates an expected call of BatchDeploy.
func (mr *MockDeployerMockRecorder) BatchDeploy(ctx, deployments, deploymentsSolutionProvider interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchDeploy", reflect.TypeOf((*MockDeployer)(nil).BatchDeploy), ctx, deployments, deploymentsSolutionProvider)
}

// Cancel mocks base method.
func (m *MockDeployer) Cancel(ctx context.Context, contractID uint64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Cancel", ctx, contractID)
	ret0, _ := ret[0].(error)
	return ret0
}

// Cancel indicates an expected call of Cancel.
func (mr *MockDeployerMockRecorder) Cancel(ctx, contractID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Cancel", reflect.TypeOf((*MockDeployer)(nil).Cancel), ctx, contractID)
}

// Deploy mocks base method.
func (m *MockDeployer) Deploy(ctx context.Context, oldDeploymentIDs map[uint32]uint64, newDeployments map[uint32]zosTypes.Deployment, newDeploymentSolutionProvider map[uint32]*uint64) (map[uint32]uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Deploy", ctx, oldDeploymentIDs, newDeployments, newDeploymentSolutionProvider)
	ret0, _ := ret[0].(map[uint32]uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Deploy indicates an expected call of Deploy.
func (mr *MockDeployerMockRecorder) Deploy(ctx, oldDeploymentIDs, newDeployments, newDeploymentSolutionProvider interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Deploy", reflect.TypeOf((*MockDeployer)(nil).Deploy), ctx, oldDeploymentIDs, newDeployments, newDeploymentSolutionProvider)
}

// GetDeployments mocks base method.
func (m *MockDeployer) GetDeployments(ctx context.Context, dls map[uint32]uint64) (map[uint32]zosTypes.Deployment, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDeployments", ctx, dls)
	ret0, _ := ret[0].(map[uint32]zosTypes.Deployment)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDeployments indicates an expected call of GetDeployments.
func (mr *MockDeployerMockRecorder) GetDeployments(ctx, dls interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDeployments", reflect.TypeOf((*MockDeployer)(nil).GetDeployments), ctx, dls)
}
