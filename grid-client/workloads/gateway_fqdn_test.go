// Package workloads includes workloads types (vm, zdb, QSFS, public IP, gateway name, gateway fqdn, disk)
package workloads

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/threefoldtech/zosbase/pkg/gridtypes"
	"github.com/threefoldtech/zosbase/pkg/gridtypes/zos"
)

// GatewayWorkload for tests
var GatewayFQDNWorkload = GatewayFQDNProxy{
	Name:           "test",
	TLSPassthrough: false,
	Backends:       []zos.Backend{zos.Backend("http://*******")},
	FQDN:           "test",
}

func TestGatewayFQDNProxyWorkload(t *testing.T) {
	var gateway gridtypes.Workload

	t.Run("test_gateway_from_zos_workload", func(t *testing.T) {
		gateway = GatewayFQDNWorkload.ZosWorkload()

		gatewayFromWorkload, err := NewGatewayFQDNProxyFromZosWorkload(gateway)
		assert.NoError(t, err)

		assert.Equal(t, gatewayFromWorkload, GatewayFQDNWorkload)
	})

	t.Run("failed to get workload data", func(t *testing.T) {
		gatewayCp := gateway
		gatewayCp.Data = nil
		_, err := NewGatewayFQDNProxyFromZosWorkload(gatewayCp)
		assert.Contains(t, err.Error(), "failed to get workload data")
	})
}
