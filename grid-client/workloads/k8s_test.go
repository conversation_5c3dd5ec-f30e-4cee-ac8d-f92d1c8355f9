// Package workloads includes workloads types (vm, zdb, QSFS, public IP, gateway name, gateway fqdn, disk)
package workloads

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/threefoldtech/zosbase/pkg/gridtypes"
	"github.com/threefoldtech/zosbase/pkg/gridtypes/zos"
)

// K8sWorkload to be used in tests
var K8sWorkload = K8sNode{
	VM: &VM{
		Name:          "test",
		NetworkName:   "network",
		NodeID:        1,
		PublicIP:      false,
		PublicIP6:     false,
		Planetary:     false,
		Flist:         K8sFlist,
		FlistChecksum: "f25c9e75e8df3b4fab3ee4de25fa5e17",
		ComputedIP:    "",
		ComputedIP6:   "",
		PlanetaryIP:   "",
		IP:            "",
		CPU:           2,
		MemoryMB:      1024,
	},
	DiskSizeGB: 5,
}

func TestK8sNodeData(t *testing.T) {
	var cluster K8sCluster
	var k8sWorkloads []gridtypes.Workload

	t.Run("test k8s workload to/from map", func(t *testing.T) {
		k8sMap, err := ToMap(K8sWorkload)
		assert.NoError(t, err)

		k8sFromMap, err := NewWorkloadFromMap(k8sMap, &K8sNode{})
		assert.NoError(t, err)
		assert.Equal(t, k8sFromMap, &K8sWorkload)
	})

	t.Run("test_new_k8s_cluster", func(t *testing.T) {
		cluster = K8sCluster{
			Master:        &K8sWorkload,
			Workers:       []K8sNode{},
			Token:         "testToken",
			SSHKey:        "",
			NetworkName:   "network",
			Flist:         K8sFlist,
			FlistChecksum: "e71ee7421f45392fbbb92309182e3006",
			Entrypoint:    "/sbin/zinit init",
		}
	})

	t.Run("test_validate", func(t *testing.T) {
		err := cluster.Validate()
		assert.NoError(t, err)
	})

	t.Run("test_generate_k8s_workloads", func(t *testing.T) {
		k8sWorkloads = K8sWorkload.MasterZosWorkload(&cluster)

		assert.Equal(t, k8sWorkloads[0].Type, zos.ZMountType)
		assert.Equal(t, k8sWorkloads[1].Type, zos.ZMachineType)
		assert.Equal(t, len(k8sWorkloads), 2)
	})

	t.Run("test_k8s_from_workload", func(t *testing.T) {
		k8s := k8sWorkloads[1]
		k8sFromWorkload, err := NewK8sNodeFromWorkload(k8s, 1, 5, "", "")
		assert.NoError(t, err)

		k8sFromWorkload.IP = ""
		k8sFromWorkload.EnvVars = nil
		assert.Equal(t, k8sFromWorkload, K8sWorkload)
	})

	t.Run("test_generate_k8s_workloads_from_cluster", func(t *testing.T) {
		k8sWorkloads, err := cluster.ZosWorkloads()
		assert.NoError(t, err)
		assert.Equal(t, k8sWorkloads[0].Type, zos.ZMountType)
		assert.Equal(t, k8sWorkloads[1].Type, zos.ZMachineType)
		assert.Equal(t, len(k8sWorkloads), 2)
	})
}
