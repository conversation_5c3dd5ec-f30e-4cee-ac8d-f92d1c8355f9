# This is an example .goreleaser.yml file with some sensible defaults.
# Make sure to check the documentation at https://goreleaser.com
version: 2
builds:
  - dir: ./farmerbot
    env:
      - CGO_ENABLED=0
    goos:
      - linux
      - windows
      - darwin
    binary: farmerbot
    id: farmerbot

    ignore:
      - goos: windows
    ldflags:
      - -X github.com/threefoldtech/tfgrid-sdk-go/farmerbot/version.Version={{.Tag}}

  - dir: ./grid-cli
    env:
      - CGO_ENABLED=0
    goos:
      - linux
      - windows
      - darwin
    binary: tfcmd
    id: tfcmd

    ignore:
      - goos: windows
    ldflags:
      - -X github.com/threefoldtech/tfgrid-sdk-go/grid-cli/cmd.version={{.Tag}}
      - -X github.com/threefoldtech/tfgrid-sdk-go/grid-cli/cmd.commit={{.Commit}}

  - dir: ./grid-cli
    env:
      - CGO_ENABLED=0
    goos:
      - linux
      - windows
      - darwin
    binary: tf-grid-cli
    id: tf-grid-cli

    ignore:
      - goos: windows
    ldflags:
      - -X github.com/threefoldtech/tfgrid-sdk-go/grid-cli/cmd.version={{.Tag}}
      - -X github.com/threefoldtech/tfgrid-sdk-go/grid-cli/cmd.commit={{.Commit}}

  - dir: ./tfrobot
    env:
      - CGO_ENABLED=0
    goos:
      - linux
      - windows
      - darwin
    binary: tfrobot
    id: tfrobot

    ignore:
      - goos: windows
    ldflags:
      - -X github.com/threefoldtech/tfgrid-sdk-go/tfrobot/cmd.version={{.Tag}}
      - -X github.com/threefoldtech/tfgrid-sdk-go/tfrobot/cmd.commit={{.Commit}}

  - dir: ./grid-proxy/cmds/proxy_server
    env:
      - CGO_ENABLED=0
    goos:
      - linux
      - windows
      - darwin
    binary: tfgridproxy
    id: tfgridproxy

    ignore:
      - goos: windows
    ldflags:
      - -w -s -X github.com/threefoldtech/tfgrid-sdk-go/grid-proxy/cmd.version={{.Tag}} -extldflags '-static'
      - -X github.com/threefoldtech/tfgrid-sdk-go/grid-proxy/cmd.commit={{.Commit}}

  - dir: ./gridify
    env:
      - CGO_ENABLED=0
    goos:
      - linux
      - windows
      - darwin
    binary: gridify
    id: gridify

    ignore:
      - goos: windows
    ldflags:
      - -X github.com/threefoldtech/tfgrid-sdk-go/gridify/cmd.version={{.Tag}}
      - -X github.com/threefoldtech/tfgrid-sdk-go/gridify/cmd.commit={{.Commit}}

  - dir: ./monitoring-bot
    env:
      - CGO_ENABLED=0
    goos:
      - linux
      - windows
      - darwin
    binary: monitoring-bot
    id: monitoring-bot

    ignore:
      - goos: windows
    ldflags:
      - -X github.com/threefoldtech/tfgrid-sdk-go/monitoring-bot/cmd.version={{.Tag}}
      - -X github.com/threefoldtech/tfgrid-sdk-go/monitoring-bot/cmd.commit={{.Commit}}

  # - dir: ./tools/relay-cache-warmer
  #   goos:
  #     - linux
  #     - windows
  #     - darwin
  #   binary: cache-warmer
  #   id: cache-warmer

  #   ignore:
  #     - goos: windows
  #   ldflags:
  #     - -X main.version={{.Tag}}
  #     - -X main.commit={{.Commit}}

archives:
  - formats: ['tar.gz']
    # this name template makes the OS and Arch compatible with the results of uname.
    name_template: >-
      {{ .ProjectName }}_
      {{- title .Os }}_
      {{- if eq .Arch "amd64" }}x86_64
      {{- else if eq .Arch "386" }}i386
      {{- else }}{{ .Arch }}{{ end }}
      {{- if .Arm }}v{{ .Arm }}{{ end }}
    # use zip for windows archives
    format_overrides:
      - goos: windows
        formats: ['zip']
checksum:
  name_template: "checksums.txt"
snapshot:
  version_template: "{{ incpatch .Version }}-next"
changelog:
  sort: asc
  filters:
    exclude:
      - "^docs:"
      - "^test:"
# The lines beneath this are called `modelines`. See `:help modeline`
# Feel free to remove those if you don't want/use them.
# yaml-language-server: $schema=https://goreleaser.com/static/schema.json
# vim: set ts=2 sw=2 tw=0 fo=cnqoj
