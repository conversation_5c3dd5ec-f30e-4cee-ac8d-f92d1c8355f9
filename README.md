# tfgrid-sdk-go

[![Codacy Badge](https://app.codacy.com/project/badge/Grade/cd6e18aac6be404ab89ec160b4b36671)](https://www.codacy.com/gh/threefoldtech/tfgrid-sdk-go/dashboard?utm_source=github.com&amp;utm_medium=referral&amp;utm_content=threefoldtech/tfgrid-sdk-go&amp;utm_campaign=Badge_Grade) [![Dependabot](https://badgen.net/badge/Dependabot/enabled/green?icon=dependabot)](https://dependabot.com/) [![Lint](https://github.com/threefoldtech/tfgrid-sdk-go/actions/workflows/lint.yml/badge.svg?branch=development)](https://github.com/threefoldtech/tfgrid-sdk-go/actions/workflows/lint.yml)
[![Test](https://github.com/threefoldtech/tfgrid-sdk-go/actions/workflows/test.yml/badge.svg?branch=development)](https://github.com/threefoldtech/tfgrid-sdk-go/actions/workflows/test.yml) [![Build](https://github.com/threefoldtech/tfgrid-sdk-go/actions/workflows/build.yml/badge.svg?branch=development)](https://github.com/threefoldtech/tfgrid-sdk-go/actions/workflows/build.yml)

This repo contains the go clients for Threefold grid.

## Packages

-   [grid client](./grid-client/README.md)
-   [grid proxy](./grid-proxy/README.md)
-   [grid cli](./grid-cli/README.md)
-   [rmb client](./rmb-sdk-go/README.md)
-   [gridify](./gridify/README.md)
-   [monitoring bot](./monitoring-bot/README.md)
-   [tfrobot](./tfrobot/README.md)
-   [user contracts mon](./user-contracts-mon/README.md)
-   [activation service](./activation-service/README.md)
-   [farmerbot](./farmerbot/README.md)

## Release

-   [release document](./docs/release.md)
-   [release script](./release.sh)
