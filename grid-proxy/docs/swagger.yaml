basePath: /
definitions:
  explorer.PingMessage:
    properties:
      ping:
        example: pong
        type: string
    type: object
  gridtypes.Unit:
    enum:
    - 1024
    - 1048576
    - 1073741824
    - 1099511627776
    type: integer
    x-enum-varnames:
    - Kilobyte
    - Megabyte
    - Gigabyte
    - Terabyte
  types.BIOS:
    properties:
      vendor:
        type: string
      version:
        type: string
    type: object
  types.Baseboard:
    properties:
      manufacturer:
        type: string
      product_name:
        type: string
    type: object
  types.Capacity:
    properties:
      cru:
        type: integer
      hru:
        $ref: '#/definitions/gridtypes.Unit'
      mru:
        $ref: '#/definitions/gridtypes.Unit'
      sru:
        $ref: '#/definitions/gridtypes.Unit'
    type: object
  types.CapacityResult:
    properties:
      total_resources:
        $ref: '#/definitions/types.Capacity'
      used_resources:
        $ref: '#/definitions/types.Capacity'
    type: object
  types.Contract:
    properties:
      contract_id:
        type: integer
      created_at:
        type: integer
      details: {}
      state:
        type: string
      twin_id:
        type: integer
      type:
        type: string
    type: object
  types.ContractBilling:
    properties:
      amountBilled:
        type: integer
      contract_id:
        type: integer
      discountReceived:
        type: string
      timestamp:
        type: integer
    type: object
  types.Dmi:
    properties:
      baseboard:
        $ref: '#/definitions/types.Baseboard'
      bios:
        $ref: '#/definitions/types.BIOS'
      memory:
        items:
          $ref: '#/definitions/types.Memory'
        type: array
      node_twin_id:
        type: integer
      processor:
        items:
          $ref: '#/definitions/types.Processor'
        type: array
      updated_at:
        type: integer
    type: object
  types.Farm:
    properties:
      certificationType:
        type: string
      dedicated:
        type: boolean
      farmId:
        type: integer
      name:
        type: string
      pricingPolicyId:
        type: integer
      publicIps:
        items:
          $ref: '#/definitions/types.PublicIP'
        type: array
      stellarAddress:
        type: string
      twinId:
        type: integer
    type: object
  types.Location:
    properties:
      city:
        type: string
      country:
        type: string
      region:
        type: string
      latitude:
        type: number
      longitude:
        type: number
    type: object
  types.Memory:
    properties:
      manufacturer:
        type: string
      type:
        type: string
    type: object
  types.Node:
    properties:
      certificationType:
        type: string
      city:
        type: string
      country:
        type: string
      created:
        type: integer
      dedicated:
        type: boolean
      dmi:
        $ref: '#/definitions/types.Dmi'
      extraFee:
        type: integer
      farm_free_ips:
        type: integer
      farmId:
        type: integer
      farmName:
        type: string
      farmingPolicyId:
        type: integer
      features:
        items:
          type: string
        type: array
      gpus:
        items:
          $ref: '#/definitions/types.NodeGPU'
        type: array
      gridVersion:
        type: integer
      healthy:
        type: boolean
      id:
        type: string
      inDedicatedFarm:
        type: boolean
      location:
        $ref: '#/definitions/types.Location'
      nodeId:
        type: integer
      num_gpu:
        type: integer
      power:
        $ref: '#/definitions/types.NodePower'
      price_usd:
        type: number
      publicConfig:
        $ref: '#/definitions/types.PublicConfig'
      rentContractId:
        type: integer
      rentable:
        type: boolean
      rented:
        type: boolean
      rentedByTwinId:
        type: integer
      serialNumber:
        type: string
      speed:
        $ref: '#/definitions/types.Speed'
      status:
        type: string
      total_resources:
        $ref: '#/definitions/types.Capacity'
      twinId:
        type: integer
      updatedAt:
        type: integer
      uptime:
        type: integer
      used_resources:
        $ref: '#/definitions/types.Capacity'
    type: object
  types.NodeGPU:
    properties:
      contract:
        type: integer
      device:
        type: string
      id:
        type: string
      node_twin_id:
        type: integer
      updated_at:
        type: integer
      vendor:
        type: string
    type: object
  types.NodePower:
    properties:
      state:
        type: string
      target:
        type: string
    type: object
  types.NodeStatistics:
    properties:
      open_connections:
        type: integer
      system:
        $ref: '#/definitions/types.NodeStatisticsResources'
      total:
        $ref: '#/definitions/types.NodeStatisticsResources'
      used:
        $ref: '#/definitions/types.NodeStatisticsResources'
      users:
        $ref: '#/definitions/types.NodeStatisticsUsers'
    type: object
  types.NodeStatisticsResources:
    properties:
      cru:
        type: integer
      hru:
        type: integer
      ipv4u:
        type: integer
      mru:
        type: integer
      sru:
        type: integer
    type: object
  types.NodeStatisticsUsers:
    properties:
      deployments:
        type: integer
      last_deployment_timestamp:
        type: integer
      workloads:
        type: integer
    type: object
  types.NodeWithNestedCapacity:
    properties:
      capacity:
        $ref: '#/definitions/types.CapacityResult'
      certificationType:
        type: string
      city:
        type: string
      country:
        type: string
      created:
        type: integer
      dedicated:
        type: boolean
      dmi:
        $ref: '#/definitions/types.Dmi'
      extraFee:
        type: integer
      farm_free_ips:
        type: integer
      farmId:
        type: integer
      farmName:
        type: string
      farmingPolicyId:
        type: integer
      features:
        items:
          type: string
        type: array
      gpus:
        items:
          $ref: '#/definitions/types.NodeGPU'
        type: array
      gridVersion:
        type: integer
      healthy:
        type: boolean
      id:
        type: string
      inDedicatedFarm:
        type: boolean
      location:
        $ref: '#/definitions/types.Location'
      nodeId:
        type: integer
      num_gpu:
        type: integer
      power:
        $ref: '#/definitions/types.NodePower'
      price_usd:
        type: number
      publicConfig:
        $ref: '#/definitions/types.PublicConfig'
      rentContractId:
        type: integer
      rentable:
        type: boolean
      rented:
        type: boolean
      rentedByTwinId:
        type: integer
      serialNumber:
        type: string
      speed:
        $ref: '#/definitions/types.Speed'
      status:
        description: added node status field for up or down
        type: string
      twinId:
        type: integer
      updatedAt:
        type: integer
      uptime:
        type: integer
    type: object
  types.Processor:
    properties:
      thread_count:
        type: string
      version:
        type: string
    type: object
  types.PublicConfig:
    properties:
      domain:
        type: string
      gw4:
        type: string
      gw6:
        type: string
      ipv4:
        type: string
      ipv6:
        type: string
    type: object
  types.PublicIP:
    properties:
      contract_id:
        type: integer
      farm_id:
        type: integer
      gateway:
        type: string
      id:
        type: string
      ip:
        type: string
    type: object
  types.Speed:
    properties:
      download:
        description: in bit/sec
        type: number
      node_twin_id:
        type: integer
      tcp_download_ipv6:
        description: in bit/sec
        type: number
      tcp_upload_ipv6:
        description: in bit/sec
        type: number
      udp_download_ipv4:
        description: in bit/sec
        type: number
      udp_download_ipv6:
        description: in bit/sec
        type: number
      udp_upload_ipv4:
        description: in bit/sec
        type: number
      udp_upload_ipv6:
        description: in bit/sec
        type: number
      updated_at:
        type: integer
      upload:
        description: in bit/sec let's suppose default is ipv4/tcp
        type: number
    type: object
  types.Stats:
    properties:
      accessNodes:
        type: integer
      contracts:
        type: integer
      countries:
        type: integer
      dedicatedNodes:
        type: integer
      farms:
        type: integer
      gateways:
        type: integer
      gpus:
        type: integer
      nodes:
        type: integer
      nodesDistribution:
        additionalProperties:
          type: integer
        type: object
      publicIps:
        type: integer
      totalCru:
        type: integer
      totalHru:
        type: integer
      totalMru:
        type: integer
      totalSru:
        type: integer
      twins:
        type: integer
      workloads_number:
        type: integer
    type: object
  types.Twin:
    properties:
      accountId:
        type: string
      publicKey:
        type: string
      relay:
        type: string
      twinId:
        type: integer
    type: object
  types.TwinConsumption:
    properties:
      last_hour_consumption:
        type: number
      overall_consumption:
        type: number
    type: object
info:
  contact: {}
  description: grid proxy server has the main methods to list farms, nodes, node details
    in the grid.
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html
  title: Grid Proxy Server API
  version: "1.0"
paths:
  /contracts:
    get:
      consumes:
      - application/json
      description: Get all contracts on the grid, It has pagination
      parameters:
      - description: Page number
        in: query
        name: page
        type: integer
      - description: Max result per page
        in: query
        name: size
        type: integer
      - description: Set contracts' count on headers based on filter
        in: query
        name: ret_count
        type: boolean
      - description: Get random patch of contracts
        in: query
        name: randomize
        type: boolean
      - description: Sort by specific contract field
        enum:
        - twin_id
        - contract_id
        - type
        - state
        - created_at
        in: query
        name: sort_by
        type: string
      - description: The sorting order, default is 'asc'
        enum:
        - desc
        - asc
        in: query
        name: sort_order
        type: string
      - description: contract id
        in: query
        name: contract_id
        type: integer
      - description: twin id
        in: query
        name: twin_id
        type: integer
      - description: node id which contract is deployed on in case of ('rent' or 'node'
          contracts)
        in: query
        name: node_id
        type: integer
      - description: contract name in case of 'name' contracts
        in: query
        name: name
        type: string
      - description: contract type 'node', 'name', or 'rent'
        in: query
        name: type
        type: string
      - description: contract state 'Created', 'GracePeriod', or 'Deleted'
        in: query
        name: state
        type: string
      - description: contract deployment data in case of 'node' contracts
        in: query
        name: deployment_data
        type: string
      - description: contract deployment hash in case of 'node' contracts
        in: query
        name: deployment_hash
        type: string
      - description: Min number of public ips in the 'node' contract
        in: query
        name: number_of_public_ips
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/types.Contract'
            type: array
        "400":
          description: Bad Request
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: Show contracts on the grid
      tags:
      - GridProxy
  /contracts/{contract_id}:
    get:
      consumes:
      - application/json
      description: Get data about a single contract with its id
      parameters:
      - description: Contract ID
        in: path
        name: contract_id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/types.Contract'
        "400":
          description: Bad Request
          schema:
            type: string
        "404":
          description: Not Found
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: Show single contract info
      tags:
      - Contract
  /contracts/{contract_id}/bills:
    get:
      consumes:
      - application/json
      description: Get all bills reports for a single contract with its id
      parameters:
      - description: Contract ID
        in: path
        name: contract_id
        type: integer
      - description: Page number
        in: query
        name: page
        type: integer
      - description: Max result per page
        in: query
        name: size
        type: integer
      - description: Set bill reports' count on headers
        in: query
        name: ret_count
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/types.ContractBilling'
            type: array
        "400":
          description: Bad Request
          schema:
            type: string
        "404":
          description: Not Found
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: Show single contract bills
      tags:
      - ContractBills
  /farms:
    get:
      consumes:
      - application/json
      description: Get all farms on the grid, It has pagination
      parameters:
      - description: Page number
        in: query
        name: page
        type: integer
      - description: Max result per page
        in: query
        name: size
        type: integer
      - description: Set farms' count on headers based on filter
        in: query
        name: ret_count
        type: boolean
      - description: Get random patch of farms
        in: query
        name: randomize
        type: boolean
      - description: Sort by specific farm field
        enum:
        - name
        - farm_id
        - twin_id
        - free_ips
        - total_ips
        - used_ips
        - dedicated
        in: query
        name: sort_by
        type: string
      - description: The sorting order, default is 'asc'
        enum:
        - desc
        - asc
        in: query
        name: sort_order
        type: string
      - description: Min number of free ips in the farm
        in: query
        name: free_ips
        type: integer
      - description: Min number of total ips in the farm
        in: query
        name: total_ips
        type: integer
      - description: Pricing policy id
        in: query
        name: pricing_policy_id
        type: integer
      - description: farm id
        in: query
        name: farm_id
        type: integer
      - description: twin id associated with the farm
        in: query
        name: twin_id
        type: integer
      - description: farm name
        in: query
        name: name
        type: string
      - description: farm name contains
        in: query
        name: name_contains
        type: string
      - description: certificate type NotCertified, Silver or Gold
        enum:
        - NotCertified
        - Silver
        - Gold
        in: query
        name: certification_type
        type: string
      - description: farm is dedicated
        in: query
        name: dedicated
        type: boolean
      - description: farm stellar_address
        in: query
        name: stellar_address
        type: string
      - description: Min free reservable mru for at least a single node that belongs
          to the farm, in bytes
        in: query
        name: node_free_mru
        type: integer
      - description: Min free reservable hru for at least a single node that belongs
          to the farm, in bytes
        in: query
        name: node_free_hru
        type: integer
      - description: Min free reservable sru for at least a single node that belongs
          to the farm, in bytes
        in: query
        name: node_free_sru
        type: integer
      - description: Min total cpu cores for at least a single node that belongs to
          the farm
        in: query
        name: node_total_cru
        type: integer
      - description: Node status for at least a single node that belongs to the farm
        in: query
        name: node_status
        type: string
      - description: Twin ID of user who has at least one rented node in the farm
        in: query
        name: node_rented_by
        type: integer
      - description: Twin ID of user for whom there is at least one node that is available
          to be deployed to in the farm
        in: query
        name: node_available_for
        type: integer
      - description: True for farms who have at least one node with a GPU
        in: query
        name: node_has_gpu
        type: boolean
      - description: True for farms who have at least one node with an ipv6
        in: query
        name: node_has_ipv6
        type: boolean
      - description: True for farms who have at least one certified node
        in: query
        name: node_certified
        type: boolean
      - description: filter farms with list of supported features on its nods
        in: query
        name: node_features
        type: string
      - description: farm country
        in: query
        name: country
        type: string
      - description: farm region
        in: query
        name: region
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/types.Farm'
            type: array
        "400":
          description: Bad Request
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: Show farms on the grid
      tags:
      - GridProxy
  /gateways:
    get:
      consumes:
      - application/json
      description: Get all gateways on the grid, It has pagination
      parameters:
      - description: Page number
        in: query
        name: page
        type: integer
      - description: Max result per page
        in: query
        name: size
        type: integer
      - description: Set nodes' count on headers based on filter
        in: query
        name: ret_count
        type: boolean
      - description: Get random patch of gateways
        in: query
        name: randomize
        type: boolean
      - description: Sort by specific gateway field
        enum:
        - node_id
        - farm_id
        - twin_id
        - uptime
        - created
        - updated_at
        - country
        - city
        - dedicated_farm
        - rent_contract_id
        - total_cru
        - total_mru
        - total_hru
        - total_sru
        - used_cru
        - used_mru
        - used_hru
        - used_sru
        - num_gpu
        - extra_fee
        in: query
        name: sort_by
        type: string
      - description: The sorting order, default is 'asc'
        enum:
        - desc
        - asc
        in: query
        name: sort_order
        type: string
      - description: Min free reservable mru in bytes
        in: query
        name: free_mru
        type: integer
      - description: Min free reservable hru in bytes
        in: query
        name: free_hru
        type: integer
      - description: Min free reservable sru in bytes
        in: query
        name: free_sru
        type: integer
      - description: Min number of free ips in the farm of the node
        in: query
        name: free_ips
        type: integer
      - description: 'Node status filter, ''up'': for only up nodes, ''down'': for
          only down nodes & ''standby'' for powered-off nodes by farmerbot.'
        in: query
        name: status
        type: string
      - description: Node city filter
        in: query
        name: city
        type: string
      - description: Node country filter
        in: query
        name: country
        type: string
      - description: node region
        in: query
        name: region
        type: string
      - description: Get nodes for specific farm
        in: query
        name: farm_name
        type: string
      - description: Set to true to filter access nodes with ipv4
        in: query
        name: ipv4
        type: boolean
      - description: Set to true to filter access nodes with ipv6
        in: query
        name: ipv6
        type: boolean
      - description: Set to true to filter nodes with domain
        in: query
        name: domain
        type: boolean
      - description: Set to true to get the dedicated nodes only
        in: query
        name: dedicated
        type: boolean
      - description: Set to true to get the nodes belongs to dedicated farms
        in: query
        name: in_dedicated_farm
        type: boolean
      - description: Set to true to filter the available nodes for renting
        in: query
        name: rentable
        type: boolean
      - description: Set to true to filter rented nodes
        in: query
        name: rented
        type: boolean
      - description: rented by twin id
        in: query
        name: rented_by
        type: integer
      - description: available for twin id
        in: query
        name: available_for
        type: integer
      - description: List of farms separated by comma to fetch nodes from (e.g. '1,2,3')
        in: query
        name: farm_ids
        type: string
      - description: certificate type
        enum:
        - Certified
        - DIY
        in: query
        name: certification_type
        type: string
      - description: get nodes owned by twin id
        in: query
        name: owned_by
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/types.Node'
            type: array
        "400":
          description: Bad Request
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: Show gateways on the grid
      tags:
      - GridProxy
  /gateways/{node_id}:
    get:
      consumes:
      - application/json
      description: Get all details for specific gateway hardware, capacity, DMI, hypervisor
      parameters:
      - description: Node ID
        in: path
        name: node_id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/types.NodeWithNestedCapacity'
        "400":
          description: Bad Request
          schema:
            type: string
        "404":
          description: Not Found
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: Show the details for specific gateway
      tags:
      - GridProxy
  /nodes:
    get:
      consumes:
      - application/json
      description: Get all nodes on the grid, It has pagination
      parameters:
      - description: Page number
        in: query
        name: page
        type: integer
      - description: Max result per page
        in: query
        name: size
        type: integer
      - description: Set nodes' count on headers based on filter
        in: query
        name: ret_count
        type: boolean
      - description: Get random patch of nodes
        in: query
        name: randomize
        type: boolean
      - description: Sort by specific node field
        enum:
        - status
        - node_id
        - farm_id
        - twin_id
        - uptime
        - created
        - updated_at
        - country
        - city
        - dedicated_farm
        - rent_contract_id
        - total_cru
        - total_mru
        - total_hru
        - total_sru
        - used_cru
        - used_mru
        - used_hru
        - used_sru
        - num_gpu
        - extra_fee
        in: query
        name: sort_by
        type: string
      - description: The sorting order, default is 'asc'
        enum:
        - desc
        - asc
        in: query
        name: sort_order
        type: string
      - description: a balance in usd, used to apply staking discount on nodes price
        in: query
        name: balance
        type: string
      - description: Min free reservable mru in bytes
        in: query
        name: free_mru
        type: integer
      - description: Min free reservable hru in bytes
        in: query
        name: free_hru
        type: integer
      - description: Min free reservable sru in bytes
        in: query
        name: free_sru
        type: integer
      - description: Total mru in bytes
        in: query
        name: total_mru
        type: integer
      - description: Total cru number
        in: query
        name: total_cru
        type: integer
      - description: Total sru in bytes
        in: query
        name: total_sru
        type: integer
      - description: Total hru in bytes
        in: query
        name: total_hru
        type: integer
      - description: Min number of free ips in the farm of the node
        in: query
        name: free_ips
        type: integer
      - description: 'Node status filter, ''up'': for only up nodes, ''down'': for
          only down nodes & ''standby'' for powered-off nodes by farmerbot.'
        in: query
        name: status
        type: string
      - description: Healthy nodes filter, 'true' for nodes that responded to rmb
          call in the last 5 mins
        in: query
        name: healthy
        type: boolean
      - description: Set to true to filter nodes with ipv6 available
        in: query
        name: has_ipv6
        type: boolean
      - description: Node city filter
        in: query
        name: city
        type: string
      - description: Node country filter
        in: query
        name: country
        type: string
      - description: Node region
        in: query
        name: region
        type: string
      - description: Get nodes for specific farm
        in: query
        name: farm_name
        type: string
      - description: Set to true to filter access nodes with ipv4
        in: query
        name: ipv4
        type: boolean
      - description: Set to true to filter access nodes with ipv6
        in: query
        name: ipv6
        type: boolean
      - description: Set to true to filter nodes with domain
        in: query
        name: domain
        type: boolean
      - description: Set to true to get the dedicated nodes only
        in: query
        name: dedicated
        type: boolean
      - description: Set to true to get the nodes belongs to dedicated farms
        in: query
        name: in_dedicated_farm
        type: boolean
      - description: Set to true to filter the available nodes for renting
        in: query
        name: rentable
        type: boolean
      - description: Set to true to filter rented nodes
        in: query
        name: rented
        type: boolean
      - description: rented by twin id
        in: query
        name: rented_by
        type: integer
      - description: available for twin id
        in: query
        name: available_for
        type: integer
      - description: rented by a twin id or available to rent
        in: query
        name: rentable_or_rented_by
        type: integer
      - description: List of farms separated by comma to fetch nodes from (e.g. '1,2,3')
        in: query
        name: farm_ids
        type: string
      - description: certificate type
        enum:
        - Certified
        - DIY
        in: query
        name: certification_type
        type: string
      - description: filter nodes on whether they have GPU support or not
        in: query
        name: has_gpu
        type: boolean
      - description: filter nodes based on GPU device ID
        in: query
        name: gpu_device_id
        type: string
      - description: filter nodes based on GPU device partial name
        in: query
        name: gpu_device_name
        type: string
      - description: filter nodes based on GPU vendor ID
        in: query
        name: gpu_vendor_id
        type: string
      - description: filter nodes based on GPU vendor partial name
        in: query
        name: gpu_vendor_name
        type: string
      - description: filter nodes that have available GPU
        in: query
        name: gpu_available
        type: boolean
      - description: get nodes owned by twin id
        in: query
        name: owned_by
        type: integer
      - description: get nodes with price greater than this
        in: query
        name: price_min
        type: string
      - description: get nodes with price smaller than this
        in: query
        name: price_max
        type: string
      - description: filter nodes with list of supported features
        in: query
        name: features
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/types.Node'
            type: array
        "400":
          description: Bad Request
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: Show nodes on the grid
      tags:
      - GridProxy
  /nodes/{node_id}:
    get:
      consumes:
      - application/json
      description: Get all details for specific node hardware, capacity, DMI, hypervisor
      parameters:
      - description: Node ID
        in: path
        name: node_id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/types.NodeWithNestedCapacity'
        "400":
          description: Bad Request
          schema:
            type: string
        "404":
          description: Not Found
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: Show the details for specific node
      tags:
      - GridProxy
  /nodes/{node_id}/gpu:
    get:
      consumes:
      - application/json
      description: Get node GPUs through the RMB relay
      parameters:
      - description: Node ID
        in: path
        name: node_id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/types.NodeGPU'
            type: array
        "400":
          description: Bad Request
          schema:
            type: string
        "404":
          description: Not Found
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: Show node GPUs information
      tags:
      - NodeGPUs
  /nodes/{node_id}/statistics:
    get:
      consumes:
      - application/json
      description: Get node statistics for more information about each node through
        the RMB relay
      parameters:
      - description: Node ID
        in: path
        name: node_id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/types.NodeStatistics'
        "400":
          description: Bad Request
          schema:
            type: string
        "404":
          description: Not Found
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: Show node statistics
      tags:
      - NodeStatistics
  /ping:
    get:
      consumes:
      - application/json
      description: ping the server to check if it is running
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/explorer.PingMessage'
      summary: ping the server
      tags:
      - ping
  /public_ips:
    get:
      consumes:
      - application/json
      description: Get all public ips on the grid with pagination and filtration support.
      parameters:
      - description: Page number
        in: query
        name: page
        type: integer
      - description: Max result per page
        in: query
        name: size
        type: integer
      - description: Set ips' count on headers based on filter
        in: query
        name: ret_count
        type: boolean
      - description: Get random patch of ips
        in: query
        name: randomize
        type: boolean
      - description: Sort by specific ip field
        enum:
        - ip
        - farm_id
        - contract_id
        in: query
        name: sort_by
        type: string
      - description: The sorting order, default is 'asc'
        enum:
        - desc
        - asc
        in: query
        name: sort_order
        type: string
      - description: Get ips on specific farm
        in: query
        name: farm_id
        type: integer
      - description: filter with the ip
        in: query
        name: ip
        type: string
      - description: filter with the gateway
        in: query
        name: gateway
        type: string
      - description: Get only the free ips, based on the ip have a contract id or
          not
        in: query
        name: free
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/types.PublicIP'
            type: array
        "400":
          description: Bad Request
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: Show public ips on the grid
      tags:
      - GridProxy
  /stats:
    get:
      consumes:
      - application/json
      description: Get statistics about the grid
      parameters:
      - description: 'Node status filter, ''up'': for only up nodes, ''down'': for
          only down nodes & ''standby'' for powered-off nodes by farmerbot.'
        in: query
        name: status
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/types.Stats'
            type: array
        "400":
          description: Bad Request
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: Show stats about the grid
      tags:
      - GridProxy
  /twins:
    get:
      consumes:
      - application/json
      description: Get all twins on the grid, It has pagination
      parameters:
      - description: Page number
        in: query
        name: page
        type: integer
      - description: Max result per page
        in: query
        name: size
        type: integer
      - description: Set twins' count on headers based on filter
        in: query
        name: ret_count
        type: boolean
      - description: Get random patch of twins
        in: query
        name: randomize
        type: boolean
      - description: Sort by specific twin field
        enum:
        - relay
        - public_key
        - account_id
        - twin_id
        in: query
        name: sort_by
        type: string
      - description: The sorting order, default is 'asc'
        enum:
        - desc
        - asc
        in: query
        name: sort_order
        type: string
      - description: twin id
        in: query
        name: twin_id
        type: integer
      - description: Account address
        in: query
        name: account_id
        type: string
      - description: Relay address
        in: query
        name: relay
        type: string
      - description: Twin public key
        in: query
        name: public_key
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/types.Twin'
            type: array
        "400":
          description: Bad Request
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: Show twins on the grid
      tags:
      - GridProxy
  /twins/{twin_id}/consumption:
    get:
      consumes:
      - application/json
      description: Get a report of user spent for last hour and for lifetime
      parameters:
      - description: twin id
        in: path
        name: twin_id
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/types.TwinConsumption'
            type: array
        "400":
          description: Bad Request
          schema:
            type: string
        "404":
          description: Not Found
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            type: string
      summary: Show a report for user consumption
      tags:
      - TwinConsumption
swagger: "2.0"
