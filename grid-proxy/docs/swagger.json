{"swagger": "2.0", "info": {"description": "grid proxy server has the main methods to list farms, nodes, node details in the grid.", "title": "Grid Proxy Server API", "contact": {}, "license": {"name": "Apache 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.html"}, "version": "1.0"}, "basePath": "/", "paths": {"/contracts": {"get": {"description": "Get all contracts on the grid, It has pagination", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["GridProxy"], "summary": "Show contracts on the grid", "parameters": [{"type": "integer", "description": "Page number", "name": "page", "in": "query"}, {"type": "integer", "description": "Max result per page", "name": "size", "in": "query"}, {"type": "boolean", "description": "Set contracts' count on headers based on filter", "name": "ret_count", "in": "query"}, {"type": "boolean", "description": "Get random patch of contracts", "name": "randomize", "in": "query"}, {"enum": ["twin_id", "contract_id", "type", "state", "created_at"], "type": "string", "description": "Sort by specific contract field", "name": "sort_by", "in": "query"}, {"enum": ["desc", "asc"], "type": "string", "description": "The sorting order, default is 'asc'", "name": "sort_order", "in": "query"}, {"type": "integer", "description": "contract id", "name": "contract_id", "in": "query"}, {"type": "integer", "description": "twin id", "name": "twin_id", "in": "query"}, {"type": "integer", "description": "node id which contract is deployed on in case of ('rent' or 'node' contracts)", "name": "node_id", "in": "query"}, {"type": "string", "description": "contract name in case of 'name' contracts", "name": "name", "in": "query"}, {"type": "string", "description": "contract type 'node', 'name', or 'rent'", "name": "type", "in": "query"}, {"type": "string", "description": "contract state 'Created', 'GracePeriod', or 'Deleted'", "name": "state", "in": "query"}, {"type": "string", "description": "contract deployment data in case of 'node' contracts", "name": "deployment_data", "in": "query"}, {"type": "string", "description": "contract deployment hash in case of 'node' contracts", "name": "deployment_hash", "in": "query"}, {"type": "integer", "description": "Min number of public ips in the 'node' contract", "name": "number_of_public_ips", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/types.Contract"}}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/contracts/{contract_id}": {"get": {"description": "Get data about a single contract with its id", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Contract"], "summary": "Show single contract info", "parameters": [{"type": "integer", "description": "Contract ID", "name": "contract_id", "in": "path"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/types.Contract"}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "404": {"description": "Not Found", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/contracts/{contract_id}/bills": {"get": {"description": "Get all bills reports for a single contract with its id", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["ContractBills"], "summary": "Show single contract bills", "parameters": [{"type": "integer", "description": "Contract ID", "name": "contract_id", "in": "path"}, {"type": "integer", "description": "Page number", "name": "page", "in": "query"}, {"type": "integer", "description": "Max result per page", "name": "size", "in": "query"}, {"type": "boolean", "description": "Set bill reports' count on headers", "name": "ret_count", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/types.ContractBilling"}}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "404": {"description": "Not Found", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/farms": {"get": {"description": "Get all farms on the grid, It has pagination", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["GridProxy"], "summary": "Show farms on the grid", "parameters": [{"type": "integer", "description": "Page number", "name": "page", "in": "query"}, {"type": "integer", "description": "Max result per page", "name": "size", "in": "query"}, {"type": "boolean", "description": "Set farms' count on headers based on filter", "name": "ret_count", "in": "query"}, {"type": "boolean", "description": "Get random patch of farms", "name": "randomize", "in": "query"}, {"enum": ["name", "farm_id", "twin_id", "free_ips", "total_ips", "used_ips", "dedicated"], "type": "string", "description": "Sort by specific farm field", "name": "sort_by", "in": "query"}, {"enum": ["desc", "asc"], "type": "string", "description": "The sorting order, default is 'asc'", "name": "sort_order", "in": "query"}, {"type": "integer", "description": "Min number of free ips in the farm", "name": "free_ips", "in": "query"}, {"type": "integer", "description": "Min number of total ips in the farm", "name": "total_ips", "in": "query"}, {"type": "integer", "description": "Pricing policy id", "name": "pricing_policy_id", "in": "query"}, {"type": "integer", "description": "farm id", "name": "farm_id", "in": "query"}, {"type": "integer", "description": "twin id associated with the farm", "name": "twin_id", "in": "query"}, {"type": "string", "description": "farm name", "name": "name", "in": "query"}, {"type": "string", "description": "farm name contains", "name": "name_contains", "in": "query"}, {"enum": ["NotCertified", "Silver", "Gold"], "type": "string", "description": "certificate type NotCertified, Silver or Gold", "name": "certification_type", "in": "query"}, {"type": "boolean", "description": "farm is dedicated", "name": "dedicated", "in": "query"}, {"type": "string", "description": "farm stellar_address", "name": "stellar_address", "in": "query"}, {"type": "integer", "description": "Min free reservable mru for at least a single node that belongs to the farm, in bytes", "name": "node_free_mru", "in": "query"}, {"type": "integer", "description": "Min free reservable hru for at least a single node that belongs to the farm, in bytes", "name": "node_free_hru", "in": "query"}, {"type": "integer", "description": "Min free reservable sru for at least a single node that belongs to the farm, in bytes", "name": "node_free_sru", "in": "query"}, {"type": "integer", "description": "Min total cpu cores for at least a single node that belongs to the farm", "name": "node_total_cru", "in": "query"}, {"type": "string", "description": "Node status for at least a single node that belongs to the farm", "name": "node_status", "in": "query"}, {"type": "integer", "description": "Twin ID of user who has at least one rented node in the farm", "name": "node_rented_by", "in": "query"}, {"type": "integer", "description": "Twin ID of user for whom there is at least one node that is available to be deployed to in the farm", "name": "node_available_for", "in": "query"}, {"type": "boolean", "description": "True for farms who have at least one node with a GPU", "name": "node_has_gpu", "in": "query"}, {"type": "boolean", "description": "True for farms who have at least one node with an ipv6", "name": "node_has_ipv6", "in": "query"}, {"type": "boolean", "description": "True for farms who have at least one certified node", "name": "node_certified", "in": "query"}, {"type": "string", "description": "filter farms with list of supported features on its nods", "name": "node_features", "in": "query"}, {"type": "string", "description": "farm country", "name": "country", "in": "query"}, {"type": "string", "description": "farm region", "name": "region", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/types.Farm"}}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/gateways": {"get": {"description": "Get all gateways on the grid, It has pagination", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["GridProxy"], "summary": "Show gateways on the grid", "parameters": [{"type": "integer", "description": "Page number", "name": "page", "in": "query"}, {"type": "integer", "description": "Max result per page", "name": "size", "in": "query"}, {"type": "boolean", "description": "Set nodes' count on headers based on filter", "name": "ret_count", "in": "query"}, {"type": "boolean", "description": "Get random patch of gateways", "name": "randomize", "in": "query"}, {"enum": ["node_id", "farm_id", "twin_id", "uptime", "created", "updated_at", "country", "city", "dedicated_farm", "rent_contract_id", "total_cru", "total_mru", "total_hru", "total_sru", "used_cru", "used_mru", "used_hru", "used_sru", "num_gpu", "extra_fee"], "type": "string", "description": "Sort by specific gateway field", "name": "sort_by", "in": "query"}, {"enum": ["desc", "asc"], "type": "string", "description": "The sorting order, default is 'asc'", "name": "sort_order", "in": "query"}, {"type": "integer", "description": "Min free reservable mru in bytes", "name": "free_mru", "in": "query"}, {"type": "integer", "description": "Min free reservable hru in bytes", "name": "free_hru", "in": "query"}, {"type": "integer", "description": "Min free reservable sru in bytes", "name": "free_sru", "in": "query"}, {"type": "integer", "description": "Min number of free ips in the farm of the node", "name": "free_ips", "in": "query"}, {"type": "string", "description": "Node status filter, 'up': for only up nodes, 'down': for only down nodes & 'standby' for powered-off nodes by farmerbot.", "name": "status", "in": "query"}, {"type": "string", "description": "Node city filter", "name": "city", "in": "query"}, {"type": "string", "description": "Node country filter", "name": "country", "in": "query"}, {"type": "string", "description": "node region", "name": "region", "in": "query"}, {"type": "string", "description": "Get nodes for specific farm", "name": "farm_name", "in": "query"}, {"type": "boolean", "description": "Set to true to filter access nodes with ipv4", "name": "ipv4", "in": "query"}, {"type": "boolean", "description": "Set to true to filter access nodes with ipv6", "name": "ipv6", "in": "query"}, {"type": "boolean", "description": "Set to true to filter nodes with domain", "name": "domain", "in": "query"}, {"type": "boolean", "description": "Set to true to get the dedicated nodes only", "name": "dedicated", "in": "query"}, {"type": "boolean", "description": "Set to true to get the nodes belongs to dedicated farms", "name": "in_dedicated_farm", "in": "query"}, {"type": "boolean", "description": "Set to true to filter the available nodes for renting", "name": "rentable", "in": "query"}, {"type": "boolean", "description": "Set to true to filter rented nodes", "name": "rented", "in": "query"}, {"type": "integer", "description": "rented by twin id", "name": "rented_by", "in": "query"}, {"type": "integer", "description": "available for twin id", "name": "available_for", "in": "query"}, {"type": "string", "description": "List of farms separated by comma to fetch nodes from (e.g. '1,2,3')", "name": "farm_ids", "in": "query"}, {"enum": ["Certified", "DIY"], "type": "string", "description": "certificate type", "name": "certification_type", "in": "query"}, {"type": "integer", "description": "get nodes owned by twin id", "name": "owned_by", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/types.Node"}}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/gateways/{node_id}": {"get": {"description": "Get all details for specific gateway hardware, capacity, DMI, hypervisor", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["GridProxy"], "summary": "Show the details for specific gateway", "parameters": [{"type": "integer", "description": "Node ID", "name": "node_id", "in": "path"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/types.NodeWithNestedCapacity"}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "404": {"description": "Not Found", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/nodes": {"get": {"description": "Get all nodes on the grid, It has pagination", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["GridProxy"], "summary": "Show nodes on the grid", "parameters": [{"type": "integer", "description": "Page number", "name": "page", "in": "query"}, {"type": "integer", "description": "Max result per page", "name": "size", "in": "query"}, {"type": "boolean", "description": "Set nodes' count on headers based on filter", "name": "ret_count", "in": "query"}, {"type": "boolean", "description": "Get random patch of nodes", "name": "randomize", "in": "query"}, {"enum": ["status", "node_id", "farm_id", "twin_id", "uptime", "created", "updated_at", "country", "city", "dedicated_farm", "rent_contract_id", "total_cru", "total_mru", "total_hru", "total_sru", "used_cru", "used_mru", "used_hru", "used_sru", "num_gpu", "extra_fee"], "type": "string", "description": "Sort by specific node field", "name": "sort_by", "in": "query"}, {"enum": ["desc", "asc"], "type": "string", "description": "The sorting order, default is 'asc'", "name": "sort_order", "in": "query"}, {"type": "string", "description": "a balance in usd, used to apply staking discount on nodes price", "name": "balance", "in": "query"}, {"type": "integer", "description": "Min free reservable mru in bytes", "name": "free_mru", "in": "query"}, {"type": "integer", "description": "Min free reservable hru in bytes", "name": "free_hru", "in": "query"}, {"type": "integer", "description": "Min free reservable sru in bytes", "name": "free_sru", "in": "query"}, {"type": "integer", "description": "Total mru in bytes", "name": "total_mru", "in": "query"}, {"type": "integer", "description": "Total cru number", "name": "total_cru", "in": "query"}, {"type": "integer", "description": "Total sru in bytes", "name": "total_sru", "in": "query"}, {"type": "integer", "description": "Total hru in bytes", "name": "total_hru", "in": "query"}, {"type": "integer", "description": "Min number of free ips in the farm of the node", "name": "free_ips", "in": "query"}, {"type": "string", "description": "Node status filter, 'up': for only up nodes, 'down': for only down nodes & 'standby' for powered-off nodes by farmerbot.", "name": "status", "in": "query"}, {"type": "boolean", "description": "Healthy nodes filter, 'true' for nodes that responded to rmb call in the last 5 mins", "name": "healthy", "in": "query"}, {"type": "boolean", "description": "Set to true to filter nodes with ipv6 available", "name": "has_ipv6", "in": "query"}, {"type": "string", "description": "Node city filter", "name": "city", "in": "query"}, {"type": "string", "description": "Node country filter", "name": "country", "in": "query"}, {"type": "string", "description": "Node region", "name": "region", "in": "query"}, {"type": "string", "description": "Get nodes for specific farm", "name": "farm_name", "in": "query"}, {"type": "boolean", "description": "Set to true to filter access nodes with ipv4", "name": "ipv4", "in": "query"}, {"type": "boolean", "description": "Set to true to filter access nodes with ipv6", "name": "ipv6", "in": "query"}, {"type": "boolean", "description": "Set to true to filter nodes with domain", "name": "domain", "in": "query"}, {"type": "boolean", "description": "Set to true to get the dedicated nodes only", "name": "dedicated", "in": "query"}, {"type": "boolean", "description": "Set to true to get the nodes belongs to dedicated farms", "name": "in_dedicated_farm", "in": "query"}, {"type": "boolean", "description": "Set to true to filter the available nodes for renting", "name": "rentable", "in": "query"}, {"type": "boolean", "description": "Set to true to filter rented nodes", "name": "rented", "in": "query"}, {"type": "integer", "description": "rented by twin id", "name": "rented_by", "in": "query"}, {"type": "integer", "description": "available for twin id", "name": "available_for", "in": "query"}, {"type": "integer", "description": "rented by a twin id or available to rent", "name": "rentable_or_rented_by", "in": "query"}, {"type": "string", "description": "List of farms separated by comma to fetch nodes from (e.g. '1,2,3')", "name": "farm_ids", "in": "query"}, {"enum": ["Certified", "DIY"], "type": "string", "description": "certificate type", "name": "certification_type", "in": "query"}, {"type": "boolean", "description": "filter nodes on whether they have GPU support or not", "name": "has_gpu", "in": "query"}, {"type": "string", "description": "filter nodes based on GPU device ID", "name": "gpu_device_id", "in": "query"}, {"type": "string", "description": "filter nodes based on GPU device partial name", "name": "gpu_device_name", "in": "query"}, {"type": "string", "description": "filter nodes based on GPU vendor ID", "name": "gpu_vendor_id", "in": "query"}, {"type": "string", "description": "filter nodes based on GPU vendor partial name", "name": "gpu_vendor_name", "in": "query"}, {"type": "boolean", "description": "filter nodes that have available GPU", "name": "gpu_available", "in": "query"}, {"type": "integer", "description": "get nodes owned by twin id", "name": "owned_by", "in": "query"}, {"type": "string", "description": "get nodes with price greater than this", "name": "price_min", "in": "query"}, {"type": "string", "description": "get nodes with price smaller than this", "name": "price_max", "in": "query"}, {"type": "string", "description": "filter nodes with list of supported features", "name": "features", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/types.Node"}}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/nodes/{node_id}": {"get": {"description": "Get all details for specific node hardware, capacity, DMI, hypervisor", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["GridProxy"], "summary": "Show the details for specific node", "parameters": [{"type": "integer", "description": "Node ID", "name": "node_id", "in": "path"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/types.NodeWithNestedCapacity"}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "404": {"description": "Not Found", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/nodes/{node_id}/gpu": {"get": {"description": "Get node GPUs through the RMB relay", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["NodeGPUs"], "summary": "Show node GPUs information", "parameters": [{"type": "integer", "description": "Node ID", "name": "node_id", "in": "path"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/types.NodeGPU"}}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "404": {"description": "Not Found", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/nodes/{node_id}/statistics": {"get": {"description": "Get node statistics for more information about each node through the RMB relay", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["NodeStatistics"], "summary": "Show node statistics", "parameters": [{"type": "integer", "description": "Node ID", "name": "node_id", "in": "path"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/types.NodeStatistics"}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "404": {"description": "Not Found", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/ping": {"get": {"description": "ping the server to check if it is running", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["ping"], "summary": "ping the server", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/explorer.PingMessage"}}}}}, "/public_ips": {"get": {"description": "Get all public ips on the grid with pagination and filtration support.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["GridProxy"], "summary": "Show public ips on the grid", "parameters": [{"type": "integer", "description": "Page number", "name": "page", "in": "query"}, {"type": "integer", "description": "Max result per page", "name": "size", "in": "query"}, {"type": "boolean", "description": "Set ips' count on headers based on filter", "name": "ret_count", "in": "query"}, {"type": "boolean", "description": "Get random patch of ips", "name": "randomize", "in": "query"}, {"enum": ["ip", "farm_id", "contract_id"], "type": "string", "description": "Sort by specific ip field", "name": "sort_by", "in": "query"}, {"enum": ["desc", "asc"], "type": "string", "description": "The sorting order, default is 'asc'", "name": "sort_order", "in": "query"}, {"type": "integer", "description": "Get ips on specific farm", "name": "farm_id", "in": "query"}, {"type": "string", "description": "filter with the ip", "name": "ip", "in": "query"}, {"type": "string", "description": "filter with the gateway", "name": "gateway", "in": "query"}, {"type": "boolean", "description": "Get only the free ips, based on the ip have a contract id or not", "name": "free", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/types.PublicIP"}}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/stats": {"get": {"description": "Get statistics about the grid", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["GridProxy"], "summary": "Show stats about the grid", "parameters": [{"type": "string", "description": "Node status filter, 'up': for only up nodes, 'down': for only down nodes & 'standby' for powered-off nodes by farmerbot.", "name": "status", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/types.Stats"}}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/twins": {"get": {"description": "Get all twins on the grid, It has pagination", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["GridProxy"], "summary": "Show twins on the grid", "parameters": [{"type": "integer", "description": "Page number", "name": "page", "in": "query"}, {"type": "integer", "description": "Max result per page", "name": "size", "in": "query"}, {"type": "boolean", "description": "Set twins' count on headers based on filter", "name": "ret_count", "in": "query"}, {"type": "boolean", "description": "Get random patch of twins", "name": "randomize", "in": "query"}, {"enum": ["relay", "public_key", "account_id", "twin_id"], "type": "string", "description": "Sort by specific twin field", "name": "sort_by", "in": "query"}, {"enum": ["desc", "asc"], "type": "string", "description": "The sorting order, default is 'asc'", "name": "sort_order", "in": "query"}, {"type": "integer", "description": "twin id", "name": "twin_id", "in": "query"}, {"type": "string", "description": "Account address", "name": "account_id", "in": "query"}, {"type": "string", "description": "Relay address", "name": "relay", "in": "query"}, {"type": "string", "description": "Twin public key", "name": "public_key", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/types.Twin"}}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}, "/twins/{twin_id}/consumption": {"get": {"description": "Get a report of user spent for last hour and for lifetime", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["TwinConsumption"], "summary": "Show a report for user consumption", "parameters": [{"type": "integer", "description": "twin id", "name": "twin_id", "in": "path"}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/types.TwinConsumption"}}}, "400": {"description": "Bad Request", "schema": {"type": "string"}}, "404": {"description": "Not Found", "schema": {"type": "string"}}, "500": {"description": "Internal Server Error", "schema": {"type": "string"}}}}}}, "definitions": {"explorer.PingMessage": {"type": "object", "properties": {"ping": {"type": "string", "example": "pong"}}}, "gridtypes.Unit": {"type": "integer", "enum": [1024, 1048576, 1073741824, 1099511627776], "x-enum-varnames": ["Kilobyte", "Megabyte", "Gigabyte", "Terabyte"]}, "types.BIOS": {"type": "object", "properties": {"vendor": {"type": "string"}, "version": {"type": "string"}}}, "types.Baseboard": {"type": "object", "properties": {"manufacturer": {"type": "string"}, "product_name": {"type": "string"}}}, "types.Capacity": {"type": "object", "properties": {"cru": {"type": "integer"}, "hru": {"$ref": "#/definitions/gridtypes.Unit"}, "mru": {"$ref": "#/definitions/gridtypes.Unit"}, "sru": {"$ref": "#/definitions/gridtypes.Unit"}}}, "types.CapacityResult": {"type": "object", "properties": {"total_resources": {"$ref": "#/definitions/types.Capacity"}, "used_resources": {"$ref": "#/definitions/types.Capacity"}}}, "types.Contract": {"type": "object", "properties": {"contract_id": {"type": "integer"}, "created_at": {"type": "integer"}, "details": {}, "state": {"type": "string"}, "twin_id": {"type": "integer"}, "type": {"type": "string"}}}, "types.ContractBilling": {"type": "object", "properties": {"amountBilled": {"type": "integer"}, "contract_id": {"type": "integer"}, "discountReceived": {"type": "string"}, "timestamp": {"type": "integer"}}}, "types.Dmi": {"type": "object", "properties": {"baseboard": {"$ref": "#/definitions/types.Baseboard"}, "bios": {"$ref": "#/definitions/types.BIOS"}, "memory": {"type": "array", "items": {"$ref": "#/definitions/types.Memory"}}, "node_twin_id": {"type": "integer"}, "processor": {"type": "array", "items": {"$ref": "#/definitions/types.Processor"}}, "updated_at": {"type": "integer"}}}, "types.Farm": {"type": "object", "properties": {"certificationType": {"type": "string"}, "dedicated": {"type": "boolean"}, "farmId": {"type": "integer"}, "name": {"type": "string"}, "pricingPolicyId": {"type": "integer"}, "publicIps": {"type": "array", "items": {"$ref": "#/definitions/types.PublicIP"}}, "stellarAddress": {"type": "string"}, "twinId": {"type": "integer"}}}, "types.Location": {"type": "object", "properties": {"city": {"type": "string"}, "country": {"type": "string"}, "region": {"type": "string"}, "latitude": {"type": "number"}, "longitude": {"type": "number"}}}, "types.Memory": {"type": "object", "properties": {"manufacturer": {"type": "string"}, "type": {"type": "string"}}}, "types.Node": {"type": "object", "properties": {"certificationType": {"type": "string"}, "city": {"type": "string"}, "country": {"type": "string"}, "created": {"type": "integer"}, "dedicated": {"type": "boolean"}, "dmi": {"$ref": "#/definitions/types.Dmi"}, "extraFee": {"type": "integer"}, "farmId": {"type": "integer"}, "farmName": {"type": "string"}, "farm_free_ips": {"type": "integer"}, "farmingPolicyId": {"type": "integer"}, "features": {"type": "array", "items": {"type": "string"}}, "gpus": {"type": "array", "items": {"$ref": "#/definitions/types.NodeGPU"}}, "gridVersion": {"type": "integer"}, "healthy": {"type": "boolean"}, "id": {"type": "string"}, "inDedicatedFarm": {"type": "boolean"}, "location": {"$ref": "#/definitions/types.Location"}, "nodeId": {"type": "integer"}, "num_gpu": {"type": "integer"}, "power": {"$ref": "#/definitions/types.NodePower"}, "price_usd": {"type": "number"}, "publicConfig": {"$ref": "#/definitions/types.PublicConfig"}, "rentContractId": {"type": "integer"}, "rentable": {"type": "boolean"}, "rented": {"type": "boolean"}, "rentedByTwinId": {"type": "integer"}, "serialNumber": {"type": "string"}, "speed": {"$ref": "#/definitions/types.Speed"}, "status": {"type": "string"}, "total_resources": {"$ref": "#/definitions/types.Capacity"}, "twinId": {"type": "integer"}, "updatedAt": {"type": "integer"}, "uptime": {"type": "integer"}, "used_resources": {"$ref": "#/definitions/types.Capacity"}}}, "types.NodeGPU": {"type": "object", "properties": {"contract": {"type": "integer"}, "device": {"type": "string"}, "id": {"type": "string"}, "node_twin_id": {"type": "integer"}, "updated_at": {"type": "integer"}, "vendor": {"type": "string"}}}, "types.NodePower": {"type": "object", "properties": {"state": {"type": "string"}, "target": {"type": "string"}}}, "types.NodeStatistics": {"type": "object", "properties": {"open_connections": {"type": "integer"}, "system": {"$ref": "#/definitions/types.NodeStatisticsResources"}, "total": {"$ref": "#/definitions/types.NodeStatisticsResources"}, "used": {"$ref": "#/definitions/types.NodeStatisticsResources"}, "users": {"$ref": "#/definitions/types.NodeStatisticsUsers"}}}, "types.NodeStatisticsResources": {"type": "object", "properties": {"cru": {"type": "integer"}, "hru": {"type": "integer"}, "ipv4u": {"type": "integer"}, "mru": {"type": "integer"}, "sru": {"type": "integer"}}}, "types.NodeStatisticsUsers": {"type": "object", "properties": {"deployments": {"type": "integer"}, "last_deployment_timestamp": {"type": "integer"}, "workloads": {"type": "integer"}}}, "types.NodeWithNestedCapacity": {"type": "object", "properties": {"capacity": {"$ref": "#/definitions/types.CapacityResult"}, "certificationType": {"type": "string"}, "city": {"type": "string"}, "country": {"type": "string"}, "created": {"type": "integer"}, "dedicated": {"type": "boolean"}, "dmi": {"$ref": "#/definitions/types.Dmi"}, "extraFee": {"type": "integer"}, "farmId": {"type": "integer"}, "farmName": {"type": "string"}, "farm_free_ips": {"type": "integer"}, "farmingPolicyId": {"type": "integer"}, "features": {"type": "array", "items": {"type": "string"}}, "gpus": {"type": "array", "items": {"$ref": "#/definitions/types.NodeGPU"}}, "gridVersion": {"type": "integer"}, "healthy": {"type": "boolean"}, "id": {"type": "string"}, "inDedicatedFarm": {"type": "boolean"}, "location": {"$ref": "#/definitions/types.Location"}, "nodeId": {"type": "integer"}, "num_gpu": {"type": "integer"}, "power": {"$ref": "#/definitions/types.NodePower"}, "price_usd": {"type": "number"}, "publicConfig": {"$ref": "#/definitions/types.PublicConfig"}, "rentContractId": {"type": "integer"}, "rentable": {"type": "boolean"}, "rented": {"type": "boolean"}, "rentedByTwinId": {"type": "integer"}, "serialNumber": {"type": "string"}, "speed": {"$ref": "#/definitions/types.Speed"}, "status": {"description": "added node status field for up or down", "type": "string"}, "twinId": {"type": "integer"}, "updatedAt": {"type": "integer"}, "uptime": {"type": "integer"}}}, "types.Processor": {"type": "object", "properties": {"thread_count": {"type": "string"}, "version": {"type": "string"}}}, "types.PublicConfig": {"type": "object", "properties": {"domain": {"type": "string"}, "gw4": {"type": "string"}, "gw6": {"type": "string"}, "ipv4": {"type": "string"}, "ipv6": {"type": "string"}}}, "types.PublicIP": {"type": "object", "properties": {"contract_id": {"type": "integer"}, "farm_id": {"type": "integer"}, "gateway": {"type": "string"}, "id": {"type": "string"}, "ip": {"type": "string"}}}, "types.Speed": {"type": "object", "properties": {"download": {"description": "in bit/sec", "type": "number"}, "node_twin_id": {"type": "integer"}, "tcp_download_ipv6": {"description": "in bit/sec", "type": "number"}, "tcp_upload_ipv6": {"description": "in bit/sec", "type": "number"}, "udp_download_ipv4": {"description": "in bit/sec", "type": "number"}, "udp_download_ipv6": {"description": "in bit/sec", "type": "number"}, "udp_upload_ipv4": {"description": "in bit/sec", "type": "number"}, "udp_upload_ipv6": {"description": "in bit/sec", "type": "number"}, "updated_at": {"type": "integer"}, "upload": {"description": "in bit/sec let's suppose default is ipv4/tcp", "type": "number"}}}, "types.Stats": {"type": "object", "properties": {"accessNodes": {"type": "integer"}, "contracts": {"type": "integer"}, "countries": {"type": "integer"}, "dedicatedNodes": {"type": "integer"}, "farms": {"type": "integer"}, "gateways": {"type": "integer"}, "gpus": {"type": "integer"}, "nodes": {"type": "integer"}, "nodesDistribution": {"type": "object", "additionalProperties": {"type": "integer"}}, "publicIps": {"type": "integer"}, "totalCru": {"type": "integer"}, "totalHru": {"type": "integer"}, "totalMru": {"type": "integer"}, "totalSru": {"type": "integer"}, "twins": {"type": "integer"}, "workloads_number": {"type": "integer"}}}, "types.Twin": {"type": "object", "properties": {"accountId": {"type": "string"}, "publicKey": {"type": "string"}, "relay": {"type": "string"}, "twinId": {"type": "integer"}}}, "types.TwinConsumption": {"type": "object", "properties": {"last_hour_consumption": {"type": "number"}, "overall_consumption": {"type": "number"}}}}}