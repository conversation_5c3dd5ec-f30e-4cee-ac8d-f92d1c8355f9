# Default values for gridproxy.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

image:
  repository: ghcr.io/threefoldtech/tfgridproxy
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  # the image tag is the same as `appVersion` in Chart.yaml
  tag: ""

env:
  - name: "SERVER_PORT"
    value: ""
  - name: "POSTGRES_HOST"
    value: "postgres"
  - name: "POSTGRES_PORT"
    value: "5432"
  - name: "POSTGRES_DB"
    value: "name"
  - name: "POSTGRES_USER"
    value: "postgres"
  - name: "POSTGRES_PASSWORD"
    value: "123"
  - name: "MNEMONICS"
    value: ""
  - name: "TFCHAINURL"
    value: "wss://tfchain.dev.grid.tf/ws"
  - name: "RELAYURL"
    value: "wss://relay.dev.grid.tf"

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

podAnnotations: {}

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

# Change port here only if you want to use another one
service:
  type: ClusterIP
  port: 443

ingress:
  enabled: true
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-production

  host: my-host.com

resources: 
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi
  limits:
    cpu: 2000m
    memory: 2048Mi
  requests:
    cpu: 1000m
    memory: 1024Mi


autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

nodeSelector: {}

tolerations: []

affinity: {}
