{{- if .Values.ingress.enabled -}}
{{- $fullName := include "gridproxy.fullname" . -}}
{{- $svcPort := .Values.service.port -}}
{{- if semverCompare ">=1.14-0" .Capabilities.KubeVersion.GitVersion -}}
apiVersion: networking.k8s.io/v1
{{- else -}}
apiVersion: extensions/v1beta1
{{- end }}
kind: Ingress
metadata:
  name: {{ $fullName }}
  labels:
    {{- include "gridproxy.labels" . | nindent 4 }}
  {{- with .Values.ingress.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  tls:
    - hosts:
      - {{ .Values.ingress.host | quote }}
      secretName: gridproxy-tls
  rules:
    - host: {{ .Values.ingress.host | quote }}
      http:
        paths:
          - path: "/" 
            pathType: Prefix
            backend:
              service:
                name: {{ $fullName }}
                port:
                  number: {{ $svcPort }}
  {{- end }}
