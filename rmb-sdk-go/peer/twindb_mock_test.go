// Code generated by MockGen. DO NOT EDIT.
// Source: twindb.go

// Package mock_direct is a generated GoMock package.
package peer

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockTwinDB is a mock of TwinDB interface.
type MockTwinDB struct {
	ctrl     *gomock.Controller
	recorder *MockTwinDBMockRecorder
}

// MockTwinDBMockRecorder is the mock recorder for MockTwinDB.
type MockTwinDBMockRecorder struct {
	mock *MockTwinDB
}

// NewMockTwinDB creates a new mock instance.
func NewMockTwinDB(ctrl *gomock.Controller) *MockTwinDB {
	mock := &MockTwinDB{ctrl: ctrl}
	mock.recorder = &MockTwinDBMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTwinDB) EXPECT() *MockTwinDBMockRecorder {
	return m.recorder
}

// Get mocks base method.
func (m *MockTwinDB) Get(id uint32) (Twin, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", id)
	ret0, _ := ret[0].(Twin)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockTwinDBMockRecorder) Get(id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockTwinDB)(nil).Get), id)
}

// GetByPk mocks base method.
func (m *MockTwinDB) GetByPk(pk []byte) (uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByPk", pk)
	ret0, _ := ret[0].(uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByPk indicates an expected call of GetByPk.
func (mr *MockTwinDBMockRecorder) GetByPk(pk interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByPk", reflect.TypeOf((*MockTwinDB)(nil).GetByPk), pk)
}
