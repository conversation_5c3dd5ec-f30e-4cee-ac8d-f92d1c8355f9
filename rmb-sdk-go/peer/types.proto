syntax = "proto3";

option go_package = "github.com/threefoldtech/tfgrid-sdk-go/rmb-sdk-go/peer/types";

// A Request annotate this message as a request message
// with proper command
message Request {
  reserved 2;
  string command = 1;
}

// A Response annotate this message as a response message
message Response { reserved 1, 2; }

// A Error annotiate this message as an error message
message Error {
  // error code (app specific)
  uint32 code = 1;
  // error message
  string message = 2;
}

message Address {
  uint32 twin = 1;
  optional string connection = 2;
}

// an app level ping pong
// in case you are using javascript
// and cant send ping messages
// when sending Pings, both signature
// and destination are ignored
message Ping {}
// if the relay received an envelope ping,
// an envelope pong will be sent back to the
// client
message Pong {}

message Envelope {
  // uid is auto generated by rmb.
  string uid = 1;
  // client specific tags
  optional string tags = 2;
  // timestamp of sending the envlope
  uint64 timestamp = 3;
  // message TTL from the time of send
  uint64 expiration = 4;
  // sender id
  Address source = 5;
  // destination of the envlope
  Address destination = 6;
  // message inside the envlope
  oneof message {
    Request request = 7;
    Response response = 8;
    Error error = 12;
    Ping ping = 15;
    Pong pong = 16;
  }
  // signature
  optional bytes signature = 9;

  // schema of the payload of either the request or the resposne message.
  optional string schema = 10;

  // a federation url (domain)
  // if not provided the relay assumes it's a local twin
  // but if provided it can be checked against the relay
  // domain, and hence decided if message need federation
  // or local.
  optional string federation = 11;

  // pyload of the message is interpreted differently based
  // on the message filed
  oneof payload {
    bytes plain = 13;
    bytes cipher = 14;
  }

	repeated string relays = 17;
}
