// Package direct package provides the functionality to create a direct websocket connection to rmb relays without the need to rmb peers.
package peer

import (
	"bytes"
	"context"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/sha256"
	"fmt"
	"net/url"
	"runtime/debug"
	"slices"
	"strings"
	"sync"
	"time"

	"github.com/decred/dcrd/dcrec/secp256k1/v4"
	"github.com/hashicorp/go-multierror"
	"github.com/pkg/errors"
	"github.com/rs/zerolog/log"
	substrate "github.com/threefoldtech/tfchain/clients/tfchain-client-go"
	"github.com/threefoldtech/tfgrid-sdk-go/rmb-sdk-go"
	"github.com/threefoldtech/tfgrid-sdk-go/rmb-sdk-go/peer/encoder"
	"github.com/threefoldtech/tfgrid-sdk-go/rmb-sdk-go/peer/types"
	"google.golang.org/protobuf/proto"
)

const (
	KeyTypeEd25519 = "ed25519"
	KeyTypeSr25519 = "sr25519"
)

const (
	// DefaultTTL is the default time-to-live for a message in seconds
	DefaultTTL uint64 = 300 // 5 minutes
	// MaxTTL is the maximum time-to-live for a message in seconds
	MaxTTL uint64 = 1800 // 30 minutes
)

// Handler is a call back that is called with verified and decrypted incoming
// messages. An error can be non-nil error if verification or decryption failed
type Handler func(ctx context.Context, peer *Peer, env *types.Envelope, err error)

type cacheFactory = func(inner TwinDB, chainURL string) (TwinDB, error)

var (
	// ErrNoValidRelayURLs is returned when no valid relay URLs are provided.
	ErrNoValidRelayURLs = errors.New("no valid relay URLs provided")
	// ErrNoFunctionalRelayAtStartup is returned when no relay connections are functional at startup.
	ErrNoFunctionalRelayAtStartup = errors.New("no relay connections are functional at startup")
	// Peer process exit reasons
	ErrPeerContextCanceled = errors.New("peer context canceled")
	ErrPeerReaderClosed    = errors.New("peer reader channel closed")
)

type peerCfg struct {
	// Require at least one working relay at startup (default: false, for backward compatibility)
	RequireFunctionalRelayOnStartup bool
	relayURLs                       []string
	keyType                         string
	session                         string
	enableEncryption                bool
	encoder                         encoder.Encoder
	cacheFactory                    cacheFactory
	relayCooldown                   time.Duration
}

// WithRelayCooldown sets the cooldown duration for relay failover and retry logic.
// If not set, defaults to 10 seconds.
func WithRelayCooldown(d time.Duration) PeerOpt {
	return func(cfg *peerCfg) {
		cfg.relayCooldown = d
	}
}

type PeerOpt func(*peerCfg)

// WithSession set a custom session name, default is the nil session
func WithSession(session string) PeerOpt {
	return func(p *peerCfg) {
		p.session = session
	}
}

// enable or disable encryption, default is enabled
func WithEncryption(enable bool) PeerOpt {
	return func(p *peerCfg) {
		p.enableEncryption = enable
	}
}

// WithRequireFunctionalRelayOnStartup configures whether the peer should require at least one working relay at startup.
// If not set, the peer will always self-heal (default, backward compatible).
func WithRequireFunctionalRelayOnStartup(required bool) PeerOpt {
	return func(p *peerCfg) {
		p.RequireFunctionalRelayOnStartup = required
	}
}

// WithRelay set up the relay url, default is mainnet relay
func WithRelay(urls ...string) PeerOpt {
	return func(p *peerCfg) {
		p.relayURLs = urls
	}
}

// WithKeyType set up the mnemonic key type, default is Sr25519
func WithKeyType(keyType string) PeerOpt {
	return func(p *peerCfg) {
		// to ensure only ed25519 and sr25519 are used
		if keyType != KeyTypeEd25519 {
			keyType = KeyTypeSr25519
		}
		p.keyType = keyType
	}
}

// WithEncoder sets encoding of the payload default is application/json
func WithEncoder(encoder encoder.Encoder) PeerOpt {
	return func(p *peerCfg) {
		p.encoder = encoder
	}
}

// WithTwinCache cache twin information for this ttl number of seconds
// if ttl == 0, twins are cached forever
func WithTmpCacheExpiration(ttl uint64) PeerOpt {
	return func(pc *peerCfg) {
		pc.cacheFactory = func(inner TwinDB, chainURL string) (TwinDB, error) {
			return newTmpCache(ttl, inner, chainURL)
		}
	}
}

// if ttl == 0 twins are cached forever
func WithInMemoryExpiration(ttl uint64) PeerOpt {
	return func(pc *peerCfg) {
		pc.cacheFactory = func(inner TwinDB, chainURL string) (TwinDB, error) {
			return newInMemoryCache(inner, ttl), nil
		}
	}
}

// Peer exposes the functionality to talk directly to an rmb relay.
//
// Relay failover and retry is managed by a thread-safe CooldownRelaySet, which tracks relay health and cooldowns.
// The cooldown duration is configurable via WithRelayCooldown. See documentation for details.
type Peer struct {
	source   *types.Address
	signer   substrate.Identity
	twinDB   TwinDB
	privKey  *secp256k1.PrivateKey
	reader   Reader
	relayset *CooldownRelaySet // manages relay selection and cooldown
	handler  Handler
	encoder  encoder.Encoder
	relays   []string
	// internal shutdown management
	subConn *substrate.Substrate
	wg      sync.WaitGroup // tracks peer.process
	connWG  sync.WaitGroup // tracks all connection workers
}

func generateSecureKey(identity substrate.Identity) (*secp256k1.PrivateKey, error) {
	keyPair, err := identity.KeyPair()
	if err != nil {
		return nil, errors.Wrap(err, "failed to generate identity key pair")
	}

	priv := secp256k1.PrivKeyFromBytes(keyPair.Seed())
	return priv, nil
}
func validateRelayURLs(relayURLs []string) ([]*url.URL, error) {
	var validRelayURLs []*url.URL

	for _, relayURL := range relayURLs {
		parsedURL, err := url.Parse(strings.ToLower(relayURL))
		if err != nil {
			log.Warn().Err(err).Str("url", relayURL).Msg("failed to parse relay URL, skipping")
			continue
		}
		// make sure it is ws or wss
		if parsedURL.Scheme != "ws" && parsedURL.Scheme != "wss" {
			log.Warn().Str("url", relayURL).Msg("relay URL must be ws or wss, skipping")
			continue
		}
		// make sure Hostname is not empty
		if parsedURL.Hostname() == "" {
			log.Warn().Str("url", relayURL).Msg("relay URL must have a hostname, skipping")
			continue
		}
		validRelayURLs = append(validRelayURLs, parsedURL)
	}

	if len(validRelayURLs) == 0 {
		return nil, ErrNoValidRelayURLs
	}

	slices.SortFunc(validRelayURLs, func(a, b *url.URL) int {
		return strings.Compare(a.Hostname(), b.Hostname())
	})
	validRelayURLs = slices.CompactFunc(validRelayURLs, func(a, b *url.URL) bool {
		return a.Hostname() == b.Hostname()
	})
	return validRelayURLs, nil
}

// getRelayConnections tries to connect to all relays and returns only the successful ones
// getRelayConnections returns InnerConnections for all valid relay URLs
func getRelayConnections(relayURLs []string, identity substrate.Identity, session string, twinID uint32) ([]string, []InnerConnection, error) {
	validRelayURLs, err := validateRelayURLs(relayURLs)
	if err != nil {
		return nil, nil, err
	}
	connections := make([]InnerConnection, 0, len(validRelayURLs))
	hosts := make([]string, 0, len(validRelayURLs))

	for _, relayURL := range validRelayURLs {
		conn := NewConnection(identity, relayURL.String(), session, twinID)
		connections = append(connections, conn)
		host := relayURL.Hostname()
		hosts = append(hosts, host)
	}

	return hosts, connections, nil
}

func getIdentity(keytype string, mnemonics string) (substrate.Identity, error) {
	var identity substrate.Identity
	var err error

	switch keytype {
	case KeyTypeEd25519:
		identity, err = substrate.NewIdentityFromEd25519Phrase(mnemonics)
	case KeyTypeSr25519:
		identity, err = substrate.NewIdentityFromSr25519Phrase(mnemonics)
	default:
		return nil, fmt.Errorf("invalid key type %s, should be one of %s or %s ", keytype, KeyTypeEd25519, KeyTypeSr25519)
	}

	if err != nil {
		return nil, errors.Wrap(err, "failed to create identity")
	}
	return identity, nil
}

// NewPeer creates a new RMB peer client. It connects directly to the RMB-Relay, and tries to reconnect if the connection broke.
//
// You can close the connection by canceling the passed context.
//
// The relay failover and retry logic uses a cooldown-based approach. See WithRelayCooldown for configuration.
func NewPeer(
	ctx context.Context,
	mnemonics string,
	subManager substrate.Manager,
	handler Handler,
	opts ...PeerOpt,
) (*Peer, error) {
	cfg := &peerCfg{
		relayURLs:        []string{"wss://relay.grid.tf"},
		session:          "",
		enableEncryption: true,
		keyType:          KeyTypeSr25519,
		cacheFactory: func(inner TwinDB, _ string) (TwinDB, error) {
			return newInMemoryCache(inner, 0), nil
		},
	}

	for _, o := range opts {
		o(cfg)
	}

	if cfg.encoder == nil {
		cfg.encoder = encoder.NewJSONEncoder()
	}
	identity, err := getIdentity(cfg.keyType, mnemonics)
	if err != nil {
		return nil, err
	}

	subConn, err := subManager.Substrate()
	if err != nil {
		return nil, err
	}

	api, _, err := subConn.GetClient()
	if err != nil {
		return nil, err
	}

	twinDB, err := cfg.cacheFactory(NewTwinDB(subConn), api.Client.URL())
	if err != nil {
		return nil, err
	}

	id, err := twinDB.GetByPk(identity.PublicKey())
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get twin by public key")
	}

	log.Info().Uint32("twin", id).Str("session", cfg.session).Msg("starting peer")

	twin, err := twinDB.Get(id)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get twin id: %d", id)
	}

	var publicKey []byte
	var privKey *secp256k1.PrivateKey
	if cfg.enableEncryption {
		privKey, err = generateSecureKey(identity)
		if err != nil {
			return nil, errors.Wrapf(err, "could not generate secure key")
		}
		publicKey = privKey.PubKey().SerializeCompressed()
	}

	hosts, conns, err := getRelayConnections(cfg.relayURLs, identity, cfg.session, twin.ID)
	if err != nil {
		return nil, err
	}

	// Hybrid approach: if RequireFunctionalRelayOnStartup is true, require at least one working relay at startup
	if cfg.RequireFunctionalRelayOnStartup {
		firstWorking := slices.IndexFunc(conns, func(c InnerConnection) bool {
			return c.TryConnect()
		})
		if firstWorking == -1 {
			return nil, ErrNoFunctionalRelayAtStartup
		}
	}

	joinURLs := strings.Join(hosts, "_")
	if !bytes.Equal(twin.E2EKey, publicKey) || twin.Relay == nil || joinURLs != *twin.Relay {
		log.Info().Str("Relay url/s", joinURLs).Msg("twin relay/public key didn't match, updating on chain ...")
		if _, err = subConn.UpdateTwin(identity, joinURLs, publicKey); err != nil {
			return nil, errors.Wrap(err, "could not update twin relay information")
		}
	}

	reader := make(chan []byte, 1024) // buffer incoming frames to keep the connection loop responsive under bursty loads
	relayPenalties := make([]RelayPenalty, 0, len(conns))
	for i := range conns {
		conn := &conns[i]
		relayPenalties = append(relayPenalties, RelayPenalty{Relay: conn, LastErrorAt: 0})
	}

	cooldown := cfg.relayCooldown
	if cooldown == 0 {
		cooldown = 10 * time.Second // default
	}
	relayset := &CooldownRelaySet{Relays: relayPenalties, Cooldown: cooldown}

	var sessionP *string
	if cfg.session != "" {
		sessionP = &cfg.session
	}
	source := types.Address{
		Twin:       id,
		Connection: sessionP,
	}

	cl := &Peer{
		source:   &source,
		signer:   identity,
		twinDB:   twinDB,
		privKey:  privKey,
		reader:   reader,
		relayset: relayset,
		handler:  handler,
		encoder:  cfg.encoder,
		relays:   hosts,
		subConn:  subConn,
	}

	cl.wg.Add(1)
	go func() {
		defer cl.wg.Done()
		cl.process(ctx)
	}()

	// Start connections using the caller's context and track them with connWG.
	for i := range conns {
		conn := &conns[i]
		conn.Start(ctx, reader, &cl.connWG)
	}

	return cl, nil
}

// Encoder returns the peer's encoder.
func (p *Peer) Encoder() encoder.Encoder {
	return p.encoder
}

func (d *Peer) handleIncoming(incoming *types.Envelope) error {
	if time.Now().Unix() > int64(incoming.Timestamp+incoming.Expiration) {
		return fmt.Errorf("received an expired envelope")
	}

	errResp := incoming.GetError()
	if incoming.Source == nil {
		// an envelope received that has NO source twin
		// this is possible only if the relay returned an error
		// hence
		if errResp != nil {
			return errors.New(errResp.Message)
		}

		// otherwise that's a malformed message
		return fmt.Errorf("received an invalid envelope")
	}

	if err := VerifySignature(d.twinDB, incoming); err != nil {
		return errors.Wrap(err, "message signature verification failed")
	}

	if errResp != nil {
		// todo: include code also
		return errors.New(errResp.Message)
	}

	var output []byte
	switch payload := incoming.Payload.(type) {
	case *types.Envelope_Cipher:
		if d.privKey == nil {
			// we received an encrypted message while
			// we have no encryption enabled on that peer
			return fmt.Errorf("received an encrypted message while encryption is not enabled")
		}
		twin, err := d.twinDB.Get(incoming.Source.Twin)
		if err != nil {
			return errors.Wrapf(err, "failed to get twin object for %d", incoming.Source.Twin)
		}
		if len(twin.E2EKey) == 0 {
			return errors.Wrap(err, "bad twin pk")
		}
		output, err = d.decrypt(payload.Cipher, twin.E2EKey)
		if err != nil {
			return errors.Wrap(err, "could not decrypt payload")
		}

		incoming.Payload = &types.Envelope_Plain{Plain: output}
	}

	return nil
}

func (d *Peer) process(ctx context.Context) {
	var exitReason string
	var exitErr error
	defer func() {
		if r := recover(); r != nil {
			log.Error().Interface("panic", r).Bytes("stack", debug.Stack()).Msg("peer.process panic recovered")
		} else {
			log.Debug().Str("reason", exitReason).Err(exitErr).Msg("peer.process exited")
		}
	}()

	for {
		select {
		case incoming, ok := <-d.reader:
			if !ok {
				exitReason = ErrPeerReaderClosed.Error()
				exitErr = ErrPeerReaderClosed
				log.Error().Msg("reader channel closed")
				return
			}
			var env types.Envelope
			if err := proto.Unmarshal(incoming, &env); err != nil {
				log.Error().Err(err).Msg("invalid message payload; skipping")
				continue
			}
			// verify and decoding!
			err := d.handleIncoming(&env)
			// TODO: If the handler does slow or blocking work, burst loads can stall the peer processing loop.
			// TODO: Decouple with goroutines or a worker pool and use a semaphore to limit in-flight work
			// to a safe number, preventing sustained backpressure.
			d.handler(ctx, d, &env, err)
		case <-ctx.Done():
			exitReason = ErrPeerContextCanceled.Error()
			exitErr = ctx.Err()
			return
		}
	}
}

// Shutdown requests peer shutdown and waits up to ctx.Deadline for completion.
// Returns ctx.Err() if the deadline elapses before shutdown completes.
// Wait blocks until all peer goroutines (process + connections) have exited.
// Caller should cancel the context passed to NewPeer() to initiate shutdown.
func (p *Peer) Wait() {
	if p == nil {
		return
	}
	p.connWG.Wait()
	p.wg.Wait()
	if p.subConn != nil {
		log.Debug().Msg("closing substrate connection")
		p.subConn.Close()
	}
}

func newAEAD(key []byte) (cipher.AEAD, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}
	return cipher.NewGCM(block)
}

func generateNonce(size int) ([]byte, error) {
	nonce := make([]byte, size)
	_, err := rand.Read(nonce)
	if err != nil {
		return nil, err
	}

	return nonce, nil
}

func (d *Peer) generateSharedSect(pubkey *secp256k1.PublicKey) [32]byte {
	point := secp256k1.GenerateSharedSecret(d.privKey, pubkey)
	return sha256.Sum256(point)
}

func (d *Peer) encrypt(data []byte, pubKey []byte) ([]byte, error) {
	secPubKey, err := secp256k1.ParsePubKey(pubKey)
	if err != nil {
		return nil, errors.Wrapf(err, "could not parse dest public key")
	}
	sharedSecret := d.generateSharedSect(secPubKey)
	// Using ECDHE, derive a shared symmetric key for encryption of the plaintext.
	aead, err := newAEAD(sharedSecret[:])
	if err != nil {
		return nil, errors.Wrap(err, "failed to create AEAD {}")
	}

	nonce, err := generateNonce(aead.NonceSize())
	if err != nil {
		return nil, errors.Wrap(err, "could not generate nonce")
	}
	cipherText := make([]byte, len(nonce))
	copy(cipherText, nonce)
	cipherText = aead.Seal(cipherText, nonce, data, nil)
	return cipherText, nil
}

func (d *Peer) decrypt(data []byte, pubKey []byte) ([]byte, error) {
	secPubKey, err := secp256k1.ParsePubKey(pubKey)
	if err != nil {
		return nil, errors.Wrapf(err, "could not parse dest public key")
	}
	sharedSecret := d.generateSharedSect(secPubKey)
	aead, err := newAEAD(sharedSecret[:])
	if err != nil {
		return nil, errors.Wrap(err, "failed to create AEAD")
	}
	if len(data) < aead.NonceSize() {
		return nil, errors.Errorf("Invalid cipher")
	}
	nonce := data[:aead.NonceSize()]

	decrypted, err := aead.Open(nil, nonce, data[aead.NonceSize():], nil)
	if err != nil {
		return nil, errors.Wrap(err, "could not decrypt message")
	}
	return decrypted, nil
}

func (d *Peer) makeEnvelope(id string, dest uint32, session *string, cmd *string, err error, data []byte, ttl uint64) (*types.Envelope, error) {
	schema := d.encoder.Schema()

	env := types.Envelope{
		Uid:        id,
		Timestamp:  uint64(time.Now().Unix()),
		Expiration: ttl,
		Source:     d.source,
		Destination: &types.Address{
			Twin:       dest,
			Connection: session,
		},
		Schema: &schema,
		Relays: d.relays,
	}

	if err != nil {
		env.Message = &types.Envelope_Error{
			Error: &types.Error{
				Message: err.Error(),
			},
		}
	} else if cmd == nil {
		env.Message = &types.Envelope_Response{
			Response: &types.Response{},
		}
	} else {
		env.Message = &types.Envelope_Request{
			Request: &types.Request{
				Command: *cmd,
			},
		}
	}

	destTwin, err := d.twinDB.Get(dest)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to get twin for %d", dest)
	}

	if len(destTwin.E2EKey) > 0 && d.privKey != nil {
		// destination public key is set, use e2e
		cipher, err := d.encrypt(data, destTwin.E2EKey)
		if err != nil {
			return nil, errors.Wrapf(err, "could not encrypt data")
		}
		env.Payload = &types.Envelope_Cipher{
			Cipher: cipher,
		}

	} else {
		env.Payload = &types.Envelope_Plain{
			Plain: data,
		}
	}

	env.Federation = destTwin.Relay // this field is deprecated and no longer used by the relay

	toSign, err := Challenge(&env)
	if err != nil {
		return nil, err
	}

	env.Signature, err = Sign(d.signer, toSign)
	if err != nil {
		return nil, err
	}

	return &env, nil
}

func (d *Peer) send(ctx context.Context, request *types.Envelope) error {
	bytes, err := proto.Marshal(request)
	if err != nil {
		return err
	}
	var errs error

	set := d.relayset

	// Determine message deadline/expiry
	expireAt := time.Unix(int64(request.Timestamp+request.Expiration), 0)

	for time.Now().Before(expireAt) {
		now := time.Now()
		items := set.Sorted(now)

		for i := range items {
			con := items[i].Relay

			// Skip relays that are not currently connected to avoid blocking on send wait
			if !con.IsConnected() {
				continue
			}

			err := con.send(ctx, bytes)
			if err != nil {
				errs = multierror.Append(errs, err)
				set.MarkFailure(con, now)
				continue
			}
			set.MarkSuccess(con)
			return nil
		}
		time.Sleep(100 * time.Millisecond)
	}
	return errs
}

// SendRequest sends an rmb message to the relay
func (d *Peer) SendRequest(ctx context.Context, id string, twin uint32, session *string, fn string, data interface{}) error {
	payload, err := d.encoder.Encode(data)
	if err != nil {
		return errors.Wrap(err, "failed to serialize request body")
	}

	var ttl uint64
	deadline, ok := ctx.Deadline()
	if ok {
		if time.Until(deadline) < 0 {
			return errors.New("context deadline is in the past")
		}
		ttl = uint64(time.Until(deadline).Seconds())

		if ttl == 0 {
			ttl = DefaultTTL
		}

		if ttl > MaxTTL {
			ttl = MaxTTL
		}
	} else {
		ttl = DefaultTTL
	}

	request, err := d.makeEnvelope(id, twin, session, &fn, nil, payload, ttl)
	if err != nil {
		return errors.Wrap(err, "failed to build request")
	}

	if err := d.send(ctx, request); err != nil {
		return err
	}

	return nil
}

// SendResponse sends an rmb message to the relay
func (d *Peer) SendResponse(ctx context.Context, id string, twin uint32, session *string, responseError error, data interface{}, ttl uint64) error {
	payload, err := d.encoder.Encode(data)
	if err != nil {
		return errors.Wrap(err, "failed to serialize request body")
	}

	request, err := d.makeEnvelope(id, twin, session, nil, responseError, payload, ttl)
	if err != nil {
		return errors.Wrap(err, "failed to build request")
	}

	if err := d.send(ctx, request); err != nil {
		return err
	}

	return nil
}

// Json extracts the json payload envelope and validate the schema
func Json(response *types.Envelope, callBackErr error) ([]byte, error) {
	if callBackErr != nil {
		return []byte{}, callBackErr
	}

	errResp := response.GetError()
	if errResp != nil {
		return []byte{}, errors.New(errResp.Message)
	}

	resp := response.GetResponse()
	if resp == nil {
		return []byte{}, errors.New("received a non response envelope")
	}

	if response.Schema == nil || *response.Schema != rmb.DefaultSchema {
		return []byte{}, fmt.Errorf("invalid schema received expected '%s'", rmb.DefaultSchema)
	}

	output := response.Payload.(*types.Envelope_Plain).Plain
	return output, nil
}
