# Introduction

This is a `Go` example for the `RMB` [rpc client](https://github.com/threefoldtech/tfgrid-sdk-go/blob/development/rmb-sdk-go/peer/rpc.go) that can send `RMB` messages through working with zos peer

## How it works

To use the example, you needs to:

-   Set the mnemonics variable to a valid mnemonics, with an activated account on the TFChain.
-   A node id to send the call to

## Usage

Make sure to use a valid node id that the client can call with valid twinId.

Run the client and wait for the response.
