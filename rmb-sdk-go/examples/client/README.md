# Introduction

This is a `Go` example for the `RMB` [default local client](https://github.com/threefoldtech/tfgrid-sdk-go/blob/development/rmb-sdk-go/client.go) that is used send `RMB` messages.

## How it works

To use the example, you needs to:

-   A twinId of a remote calculator process that could add two integers

## Usage

Make sure to have a remote process that the client can call with valid twinId.

Run the client and wait for the response.
