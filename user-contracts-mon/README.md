# TFGrid Contract Monitor Bot

<a href='https://github.com/jpoles1/gopherbadger' target='_blank'>![gopherbadger-tag-do-not-edit](https://img.shields.io/badge/Go%20Coverage-11%25-brightgreen.svg?longCache=true&style=flat)</a>

## Overview

The Contract Monitor Bot is a tool designed to monitor user contracts within ThreeFold Grid.

**Features**:

-   Monitors user contracts and nodes status.
-   Customizable alerting and notification system.

## Getting Started

### Prerequisites

Ensure that you have installed:

-   Go programming language (version 1.19 or higher if not using docker)
-   Git

### How to start

1.  Clone this repository to your local machine:

   ```bash
   git clone https://github.com/threefoldtech/tfgrid-sdk-go.git
   cd tfgrid-sdk-go/user-contracts-mon
   ```

2.  Setup your telegram bot and your env

-   Create a new [telegram bot](README.md#create-a-bot) if you don't have.
-   Create a new env file `.env`, for example:

   ```env
        BOT_TOKEN=<your bot token>
        INTERVAL=<number of hours between notifications>
        ```

3.  Run the bot:

-   Using go

    ```bash
        go run main.go -e .env
        ```

-   Using Docker

     ```bash
        docker build -t contract-mon -f Dockerfile ../
        docker run -v $(pwd)/.env:/.env -it contract-mon -e /.env
        ```

## Create a bot

-   Open telegram app
-   Create a new bot

  ```ordered
     1. Find telegram bot named "@botfarther"
     2. Type /newbot
     ```

-   Get the bot token

  ```ordered
    1. In the same bot named "@botfarther"
    2. Type /token
    3. Choose your bot
    ```
